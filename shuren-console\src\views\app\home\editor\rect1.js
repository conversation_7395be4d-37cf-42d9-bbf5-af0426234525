import Base from './base';
// import request from "@/api/modules/request.js"

export default {
  name: 'EditorRect1',
  extends: Base,
  data() {
    return {
        textList:[]
    }
  },
  created(){
    // if(!this.$store.state.admin.pid){
    //   request.get('/v1/media.item.subList',{creator:this.$store.state.admin.id}).then((res)=>{
    //     this.textList = res
    //   }).catch(err=>{
    //     console.log(err);
    //   })
    // }else{
    //   request.get('/v1/media.item.pList').then((res)=>{
    //     this.textList = res
    //   }).catch(err=>{
    //     console.log(err);
    //   })
    // }
  },
  render(h) {
    // if(this.$store.state.admin.isSpecial){
    //   let style = this.getStyle();
    //       return ( 
    //        <div class="editor-com editor-rect" style={style} ondblclick={this.onBgImage}>
    //          <div class="editor-select-link" title="点击更改链接地址" onClick={this.onLink}><i class="el-icon-link"></i></div>
    //          <div>
    //           {this.$slots.default}  
    //           <div class="posBox" style={{width:'100%',height:'100%',borderRadio:'10px',position:'fixed',top:'0',right:'0'}}>
    //                   <vue-seamless-scroll data={this.textList} class-option={this.optionHover} class="seamless-warp">
    //                  {                  
    //                     this.textList.map(v=>{
    //                       return (
    //                         <p class="topText"  style={{width:'100%',height:'40px',display:'flex',justifyContent:'space-around',padding:'0 20px',lineHeight:'40px'}}>
    //                         <span class="nameText" style={{width:'30%',height:'100%',color:'#fdf451',fontSize:'24px',overflow:'hidden',textOverflow:'ellipsis',display:'-webkit-box',WebkitBoxOrient:'vertical',WebkitLineClamp:1,textAlign:'left'}}>{ v.name }</span>
    //                         <span class="titleText"  style={{flex:1,color:'#f1f1f1',fontSize:'24px',overflow:'ellipsis',display:'-webkit-box',WebkitBoxOrient:'vertical',WebkitLineClamp:'1',textAlign:'center',overflow:'hidden',textOverflow:'ellipsis'}}>{ v.title }</span>
    //                         <span class="timeText" style={{width:'30%',height:'100%',color:'#f1f1f1',fontSize:'20px',overflow:'hidden',textOverflow:'ellipsis',display:'-webkit-box',WebkitBoxOrient:'vertical',WebkitLineClamp:1,textAlign:'right'}}>{ v.time }</span>
    //                       </p>                         
    //                       )
    //                     })                    
    //                   }
    //                 </vue-seamless-scroll> 
    //                 <div class="bot" style={{width:'100%',height:'50px',backgroundColor:'rgba(0,0,0,0.4)',   position:'absolute',bottom:0,left:0,borderRadius:'0 0 10px 10px',fontSize:'24px',color:'white',textAlign:'center',lineHeight:'50px'}}>资讯汇聚</div>
    //                 <img src="http://cdn.dangjian.nextv.show/app/icon/9.jpg" alt="" class="img"  style=   {{width:'100%',height:'100%',opacity:'0',background:'rgba(0,0,0,0.4)',position:'absolute',top:0,rihgt:0}}/>
    //           </div> 
    //          </div> 
    //        </div>
    //      );
    // }else{
    //   let style = this.getStyle();
    //       return ( 
    //        <div class="editor-com editor-rect" style={style} ondblclick={this.onBgImage}>
    //          <div class="editor-select-link" title="点击更改链接地址" onClick={this.onLink}><i class="el-icon-link"></i></div>
    //          <div>
    //           {this.$slots.default}
    //          </div> 
    //        </div>
    //      );
      
    // }
  }
}