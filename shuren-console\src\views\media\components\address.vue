<template>
  <div v-if="">
    <el-checkbox style="margin: 0 0 0 10px"
                 v-for="item in list"
                 v-model="item.checked"
                 :disabled="disAddress"
                 @click.native.prevent.stop="disAddress || setAddress(item)"
    >{{item.text}}
    </el-checkbox>
  </div>
</template>

<script>
  export default {
    props: ['value'],
    data() {
      return {
        multiple: Array.isArray(this.value)
      }
    },
    methods: {}
  }
</script>