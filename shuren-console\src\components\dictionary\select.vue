<template>
  <el-cascader
      v-model="value"
      filterable
      clearable
      :options="options"
      :props="{ expandTrigger: 'hover', label: 'name', value: 'code', emitPath: false }"
      @change="onChange"></el-cascader>
</template>

<script>
export default {
  props: {
    value: {},
    type: {
      type: String
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      options: []
    }
  },
  created() {
    this.$api.get('/v1/sys.dictionary.items', {type: this.type}).then(list => {
      this.options = list;
    });
  },
  methods: {
    onChange(v) {
      this.$emit('input', v);
      this.$emit('change', v);
    }
  }
}
</script>