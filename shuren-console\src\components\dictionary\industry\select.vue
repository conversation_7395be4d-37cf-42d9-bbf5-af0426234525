<template>
  <el-cascader
      v-model="value"
      :options="options"
      :props="{ expandTrigger: 'hover', label: 'name', value: 'code' }"
      @change="onChange"></el-cascader>
</template>

<script>
export default {
  props: {
    value: {},
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      options: []
    }
  },
  created() {
    this.$api.get('/v1/sys.dictionary.items', {type: 'industry'}).then(list => {
      this.options = list;
    });
  },
  methods: {
    onChange() {

    }
  }
}
</script>