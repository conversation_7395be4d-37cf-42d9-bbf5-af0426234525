<template>
  <div class="edit-panel-page edit-media-panel edit-media-image">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">PPT素材</div>

        <media-image v-model="model.cover_url" style="margin-top:30px;width:100%;height:140px;"></media-image>

        <div class="edit-panel-menu">
          <div class="item" v-for="item in menu" :class="{active:active==item.name}" @click="go(item.name)">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <div class="edit-panel-submit">
          <el-button type="primary" size="small" @click="onPublish">发 布</el-button>
        </div>
      </div>
      <div class="edit-panel-content">
        <keep-alive>
          <component ref="part" v-bind:is="`edit-${active}`"></component>
        </keep-alive>
        <div class="edit-panel-action">
          <el-button size="small" @click="prev()" v-show="current>0">上一步</el-button>
          <el-button type="primary" size="small" v-show="current<menu.length-1" @click="next()">下一步</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.media-editor-bgmusic {
  padding: 30px 20px;
  background-color: #f5f7fa;

  .el-button {
    position: absolute;
    right: 2px;
    top: 2px;
    background: #fff;
    padding: 0 10px;
    bottom: 2px;
  }
}

.edit-media-image {
  .interval-slider {
    flex: 1;
    padding: 0 15px;
    background: #f1f1f1;
    margin: 0 20px;
    border-radius: 30px;

    .el-slider__runway {
      margin: 7px 0;
    }
  }

  .scroll-content {
    background: #f8f8f8;
    width: 100%;
    overflow-x: scroll;
    white-space: nowrap;
    min-height: 137px;
    margin-top: 20px;
  }

  .scroll-image {
    cursor: pointer;
    width: 210px;
    height: 120px;
    display: inline-block;

    .el-image {
      width: 100%;
      height: 100%;
    }

    & + .scroll-image {
      margin-left: 10px;
    }

    &:hover, &.is-active {
      &:after {
        content: '';
        pointer-events: none;
        touch-action: none;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        border: 2px solid #2375C8;
      }

      .actions {
        display: block;
      }
    }
  }

  .preview {
    background: #f8f8f8;
    padding-bottom: calc(100% * 9 / 16);
  }

  .preview-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .img-action {
    padding: 10px;
    color: #fff;
    text-shadow: 0 0 20px black;
  }

  .img-text {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 70%;

    textarea {
      font-size: 14px;
      color: #fff;
      text-shadow: 0 0 20px black;
      border: none;
      resize: none;
      background: none;
      padding: 20px;
    }
  }

  .img-index {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 32px;
    height: 32px;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    border-radius: 50%;
    line-height: 32px;
    transform: translate(-50%, -50%);
  }

  .actions {
    position: absolute;
    top: 0;
    right: 0;
    color: #fff;
    text-shadow: 0 0 black;
    cursor: pointer;
    display: none;
  }
}
</style>

<script>
import Base from '../../publish/panel';
import MediaImage from '@/views/media/image/cover';
import EditBase from './components/base';
import EditChannel from './components/channel';
import EditContent from './components/content';
import EditBackgroundMusic from './components/background-music';

export default {
  extends: Base,
  components: {MediaImage, EditBase, EditChannel, EditContent, EditBackgroundMusic},
  data() {
    return {
      menu: [
        {label: '基础信息', value: '', name: 'base'},
        {label: '背景音乐', value: '', name: 'background-music'},
        {label: '轮播内容', value: '', name: 'content'},
        {label: '展示位置', value: '', name: 'channel'},
      ],
      model: this.value || {
        type: 'image',
        status: 'editing',
        title: '',
        author: '',
        source: '',
        subtitle: '',
        abstract: '',
        cover_url: '',
        pubdate: null,
        autoplay: 1,
        interval: 5,
        animation: 0,
        content: [],
        bgcolor: 'rgba(0,0,0,1)',
        bgmusic: {
          url: '',
          name: '',
          loop: 1,
          volume: 1,
          autoplay: 1,
          enabled: 0
        },
        bgvoice: {
          url: '',
          name: '',
          loop: 1,
          volume: 1,
          autoplay: 1,
          enabled: 0
        },
        channels: [],
        display_date: null,
        staff_id: null
      }
    }
  }
}
</script>