<template>
  <div class="edit-panel-content">
    <div class="edit-panel-title">党员档案</div>
    <div class="edit-panel-desc">仅限中共党员、中共预备党员填写</div>

    <el-form class="edit-panel-body" ref="form" label-width="90px" label-suffix=":" :model="model" :rules="rules"
             size="small" style="padding-right:40px">
      <el-form-item label="政治面貌" prop="party_code">
        <dictionary v-model="model.party_code" type="politics" @change="onPartyCode"></dictionary>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="入党时间" prop="party_join">
            <el-date-picker v-model="model.party_join" placeholder="请选择" format="yyyy年MM月dd日"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="党内职务" prop="party_title">
            <el-input v-model.trim="model.party_title" placeholder="从高到底，多个职务请用空格隔开" maxlength="20"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!--
      <el-form-item label="所属党部">
        <department v-model="model.dept_id"></department>
      </el-form-item>
      -->
      <el-form-item label="党员评价" prop="grade" :required="true">
        <el-radio-group v-model="model.grade" size="small">
          <el-radio :label="0" border>合格</el-radio>
          <el-radio :label="1" border>优秀</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="我的承诺">
        <el-input placeholder="不忘初心，牢记使命" v-model.trim="model.party_oath" maxlength="64"></el-input>
        <cover-image v-model="model.promise_image" style="margin:30px auto auto auto;width:250px;height:150px;"
                     empty-text="我的承诺"></cover-image>
      </el-form-item>
      <el-form-item label="党员简介">
        <el-input type="textarea" placeholder="请输入" :rows="5" v-model.trim="model.party_intro" maxlength="1000"
                  show-word-limit></el-input>
      </el-form-item>
    </el-form>
    <div class="edit-panel-action">
      <el-button type="primary" @click="onSubmit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import Dictionary from '@/components/dictionary/select';
import Department from '@/components/department/select';
import CoverImage from "@/views/media/image/cover";

export default {
  components: { Dictionary, Department, CoverImage },
  data() {
    return {
      rules: {
        party_code: { required: true, trigger: 'blur', message: '必选项' },
        party_title: { required: false, trigger: 'blur', message: '必选项' },
        party_join: { required: false, trigger: 'blur', message: '必选项' }
      }
    }
  },
  computed: {
    model() {
      return this.$parent.model;
    }
  },
  methods: {
    onPartyCode(v) {
      let { rules } = this, bool = v == 1;

      rules.party_title.required = bool;
      rules.party_join.required = bool;
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        valid && this.doSubmit();
      });
    },
    doSubmit() {
      let body = {};
      let field = 'id,company_id,dept_id,party_code,party_title,party_join,party_oath,party_intro,grade,promise_image';
      field.split(',').forEach(key => {
        body[key] = this.model[key];
      });
      this.$parent.submit('/v1/company.staff.update?part=party', body);
    }
  }
}
</script>