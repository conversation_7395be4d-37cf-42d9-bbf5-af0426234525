module.exports = {
  publicPath: "/",
  productionSourceMap: false,
  configureWebpack: {
    externals: {
      vue: "Vue",
      //'vue-router': 'VueRouter',
      //'vuex': 'Vuex'
    },
  },
  devServer: {
    disableHostCheck: true,
    host: "127.0.0.1",
    // host: "***********",
    proxy: {
      account: {
        target: process.env.VUE_APP_MYNATAPP,
        changeOrigin: true,
      },
    },
    port: 4204,
    https: false,
  },
  css: {
    loaderOptions: {
      sass: {
        data: `$cnd-url:'${process.env.VUE_APP_CDN}/';@import "@/assets/var.scss";`,
      },
    },
  },
};
