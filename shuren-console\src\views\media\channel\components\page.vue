<template>
  <div>
    <mytable
      ref="table"
      :list="list"
      :api="$api.media.channel.list"
      :query="query"
      :pagination="false"
    >
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <template v-if="showSort">
              <el-button size="mini" @click="showSort = false">取消</el-button>
              <el-button type="primary" size="mini" @click="saveSort"
                >保存</el-button
              >
            </template>
            <template v-else>
              <el-button size="mini" @click="onShowSort">排序</el-button>
              <el-button type="primary" size="mini" @click="onEdit()"
                >添加</el-button
              >
            </template>
          </div>
        </div>
      </div>

      <el-table-column label="名称" prop="name"></el-table-column>
      <el-table-column label="编号" prop="id"></el-table-column>
      <el-table-column
        label="类型"
        prop="type"
        :formatter="fType"
        align="center"
      ></el-table-column>
      <el-table-column
        label="隐藏"
        prop="type"
        :formatter="fHidden"
        align="center"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        prop="created"
        :formatter="fCreate"
        align="center"
      ></el-table-column>
      <el-table-column
        label="发布内容"
        prop="item_count"
        align="center"
      ></el-table-column>
      <el-table-column
        label="显示排序"
        v-if="showSort"
        prop="sort"
        align="center"
        width="90"
        class-name="is-sort"
        :formatter="fSort"
      >
        <input
          slot-scope="scope"
          v-model="sortList[scope.row.id]"
          type="text"
        />
      </el-table-column>
      <el-table-column v-else label="操作" align="center" width="90px">
        <template slot-scope="scope">
          <span v-if="!platform && scope.row.platform">平台</span>
          <template v-else>
            <el-button
              class="btn-action"
              size="mini"
              @click="onEdit(scope.row)"
              title="编辑"
            >
              <svg class="icon" aria-hidden="true">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xlink:href="#edit"
                ></use>
              </svg>
            </el-button>
            <el-button
              class="btn-action"
              size="mini"
              @click="onDel(scope.row)"
              title="删除"
            >
              <svg class="icon" aria-hidden="true">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xlink:href="#delete"
                ></use>
              </svg>
            </el-button>
          </template>
        </template>
      </el-table-column>
    </mytable>

    <edit-dialog
      v-if="editing"
      @closed="editing = false"
      :value="model"
      :channels="list"
      @change="refresh"
    ></edit-dialog>
  </div>
</template>

<script>
import EditDialog from "./edit-dialog";

export default {
  components: { EditDialog },
  data() {
    return {
      editing: false,
      model: null,
      showSort: false,
      sortList: null,
    };
  },
  created(){
    // console.log(this.list);
    if(this.list.length >= 2){
      this.$message({
        type:"warning",
        message:"当前操作频繁，请添加二级频道后再进行添加保存，否则将不会保存"
      })
    }
  },
  computed: {
    list() {
      return [];
    },
    platform() {
      return this.$route.name == "platform.channel";
    },
    owner() {
      return this.$store.state.admin.id;
    },
    query() {
      return this.platform ? { platform: 1 } : { owner: this.owner };
    },
  },
  methods: {
    refresh() {
      this.$refs.table.refresh();
    },
    fType(row, column, val) {
      return this.$api.media.channel.type(val);
    },
    fCreate(row, column, val) {
      return this.$utils.Date(val).format("YYYY-MM-DD");
    },
    fHidden(row, column, val) {
      return val ? "是" : "否";
    },
    onEdit(row) {
      this.model = row;
      this.editing = true;
    },
    onDel(row) {
      this.$confirm("操作不可恢复，确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return this.$api.media.channel.delete(row.id);
        })
        .then(this.refresh);
    },
    onShowSort() {
      let res = {};

      function eachList(list) {
        list.forEach((item) => {
          res[item.id] = item.sort;
          if (item.children) eachList(item.children);
        });
      }

      eachList(this.$refs.table.list);

      this.sortList = res;
      this.showSort = true;
    },
    saveSort() {
      this.showSort = false;
      this.$api
        .post("/v1/media.channel.sort", this.sortList)
        .then(this.refresh);
    },
  },
};
</script>