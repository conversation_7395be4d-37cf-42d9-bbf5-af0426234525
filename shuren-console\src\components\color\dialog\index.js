import Template from './view';

let View;

export default function (color, title) {
  if (!View) {
    View = Vue.extend(Template);
  }

  let el = document.createElement('div');
  document.body.appendChild(el);

  let view = new View({
    propsData: {value: color, title: title},
    el: el
  });

  return new Promise((resolve, reject) => {
    let value;

    view.$on('change', function (v) {
      value = v;
    });

    view.$on('closed', function () {
      view.$destroy();
      value ? resolve(value) : reject();
    });

    view.visible = true;
  });
}