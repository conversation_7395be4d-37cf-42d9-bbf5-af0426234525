<template>
  <div v-loading="!!status">
    <div class="edit-panel-title">最新版本({{ model.name }})</div>

    <div class="edit-panel-body"
         style="min-height:300px;border: 1px solid #DCDFE6;border-radius: 4px;padding:20px;line-height:1.6;"
         v-html="model.content">
    </div>

    <div class="edit-panel-action">
      <el-button type="primary" @click="notifyApp">推送更新</el-button>
    </div>
  </div>
</template>

<script>
import BasePanel from './base-panel';

export default {
  extends: BasePanel,
  data() {
    return {
      model: {
        code: 0,
        name: '加载中',
        content: '更新说明',
        source: '',
        created: '',
        platform: 0
      }
    }
  },
  activated() {
    this.reload();
  },
  methods: {
    reload() {
      this.$api.get('/v1/app.version.newest', {platform: 0}).then(res => {
        this.model = res;
      });
    },
    notifyApp() {
      this.socket.emit('notify app upgrade');
      this.$message.success('已推送');
    }
  }
}
</script>