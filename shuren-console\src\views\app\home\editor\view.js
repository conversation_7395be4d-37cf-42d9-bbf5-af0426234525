import './style.scss'
import EditorRect from './rect'
import EditorRect1 from './rect1'
import EditorRect2 from './rect2'
import EditorRect3 from './rect3'
import EditorImage from './image'
import EditorLabel from './label'
import EditorLabel1 from './label1'
import EditorVideo from './video'
export default {
  name: 'EditorView',
  props: ['model'],
  components: { EditorRect, EditorImage, EditorLabel, EditorVideo, EditorLabel1, EditorRect1, EditorRect2, EditorRect3 },
  created() {
    // console.log(this.model)
  },
  methods: {
    renderChildren(h, list) {
      if (!list) return
      return list.map((model) => {
        let name = 'editor-' + model.name
        console.log(name)
        let children = this.renderChildren(h, model.children)
        // console.log(children)
        return h(name, { props: { model: model } }, children)
      })
    },
  },
  render(h) {
    let model = this.model
    let name = 'editor-' + model.name
    let children = this.renderChildren(h, model.children)
    return h(name, { props: { model: model } }, children)
  },
}
