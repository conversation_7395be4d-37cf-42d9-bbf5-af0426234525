import Base from './base';

export default {
  name: 'EditorRect',
  extends: Base,
  render(h) {
    let style = this.getStyle();
        return ( 
         <div class="editor-com editor-rect" style={style} ondblclick={this.onBgImage}>
           <div class="editor-select-link" title="点击更改链接地址" onClick={this.onLink}><i class="el-icon-link"></i></div>
           <div>
            {this.$slots.default}  
           </div> 
         </div>
       );
  }
}