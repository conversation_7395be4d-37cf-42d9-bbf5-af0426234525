<template>
  <el-dialog :visible.sync="visible" :title="title" width="560px" :append-to-body="true" @closed="onClosed">
    <color-panel v-model="value"></color-panel>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ColorPanel from '../panel';

export default {
  components: {ColorPanel},
  props: {
    title: {
      type: String,
      default: '请选择'
    },
    value: {
      type: String,
      default: 'rgba(0,0,0,0)'
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    submit() {
      this.visible = false;
      this.$emit('input', this.value);
      this.$emit('change', this.value);
    },
    onClosed() {
      this.$emit('update:visible', false);
      this.$emit('closed');
    }
  }
}
</script>