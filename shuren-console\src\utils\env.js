import {version} from '../../package';

const protocol = location.protocol == 'http:' ? 'http:' : 'https:'
  , TOKEN = 'Api-Token'
  , CDN_URL = process.env.VUE_APP_CDN;
const Env = {
  debug: process.env.NODE_ENV == 'development',
  version: version,
  canBack: 1,
  APP_MYNATAPP: process.env.VUE_APP_MYNATAPP,
  API_URL: process.env.VUE_APP_API,
  CDN_URL: CDN_URL,
  APP_URL: process.env.VUE_APP_URL,
  SOCKET_URL: process.env.VUE_APP_SOCKET,
  protocol,
  IS_HTTPS: protocol == 'https:',
  host: process.env.VUE_APP_URL,
  headimg: CDN_URL + '/sys/headimg150.jpg',
  get width() {
    return document.documentElement.clientWidth
  },
  get height() {
    return document.documentElement.clientHeight
  },
  get token() {
    let v = localStorage.getItem(TOKEN);
    if (!v) v = 'admin-' + version + '-browser'
    return v;
  },
  set token(v) {
    if (v) {
      localStorage.setItem(TOKEN, v);
    } else {
      localStorage.removeItem(TOKEN);
    }
  }
};

let search = location.search, match = search.match(/[\?&]token=\w+\.\w+\.\w+/);
if (match) {
  let token = match[0];
  search = search.replace(token, '').substr(1);
  history.replaceState(null, null, location.pathname + (search ? '?' : '') + search);
  Env.token = token.substr(7);
}

export default Env;