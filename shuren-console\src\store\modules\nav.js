import API from '@/api';

const eachPath = function (menu, parents, names) {

  menu.children && menu.children.forEach(item => {
    eachPath(item, parents.concat([menu.title]), names);
  });

  let data = {
    id: menu.id,
    title: (parents.length ? parents.join(' - ') + ' - ' : '') + menu.title,
    regexp: new RegExp('^' + menu.path),
    fixed: 0,
    isTemp: 0
  };

  menu.nodes && menu.nodes.forEach(node => {
    if (node.path) {
      names.push(Object.assign({}, data, {
        regexp: new RegExp('^' + node.path),
        title: data.title + ' - ' + node.title
      }));
    }
  });

  if (menu.path) {
    names.push(data);
  }
};

const state = {
  sidebar: [],
  routes: [],
  names: [],
  history: [],
  active: 0,
  route: null,
};      

const mutations = {
  SET_SIDEBAR(state, list) {
    let names = [];

    list.forEach(item => {
      eachPath(item, [], names);
    });

    state.names = names;
    state.sidebar = list;
  },
  SET_ACTIVE(state, route) {
    let {routes, history} = state, none = 1, index;

    // 计算选中菜单
    routes.some(item => {
      if (item.path == route.path) {
        route = item;
        none = 0;
        return true;
      }
    });

    if (none) {
      route = Object.assign(route, {
        id: Math.random(),
        title: route.title,
        fixed: 0,
        isTemp: 1
      });
    }

    // 找到最符合的
    if (route.isTemp) {
      let list = [], res;
      state.names.forEach(item => {
        res = item.regexp.exec(route.path);
        if (res) list.push({path: res[0], data: item});
      });

      if (list.length > 0) {
        list.sort(function (a, b) {
          return b.path.length - a.path.length;
        });

        let item = list[0].data;
        Object.assign(route, {id: item.id, title: item.title, isTemp: 0});
      }
    }

    // 记录顺序
    index = history.indexOf(route.path);
    if (index != -1) history.splice(index, 1);
    history.push(route.path);

    if (none) routes.push(route);
    state.active = route.id;
    state.route = route;
  },
  CLOSE(state, path) {
    let {routes, history} = state, i, index = -1, len = routes.length;

    if (len < 2) return;

    for (i = 0; i < len; i++) {
      if (routes[i].path == path) {
        index = i;
        break;
      }
    }

    if (index == -1) return;

    let route = routes[index], h = history.indexOf(route.path);
    routes.splice(index, 1);
    history.splice(h, 1);
  },
  // CHANGE(state,payload){
  //   state.isShowZhan = payload
  // }
};

const actions = {
  sidebar({commit, state}) {
    API.menu.sidebar().then(list => {
      commit('SET_SIDEBAR', list);
      commit('SET_ACTIVE', state.route);
    });
  },
  active({commit}, route) {
    return commit('SET_ACTIVE', {
      title: route.meta.title,
      name: route.name,
      path: route.fullPath
    });
  },
  close({commit}, route) {
    return commit('CLOSE', route.path);
  },
  // 发送资讯给下级
  // showChange({commit},state){
  //   console.log(state);

  //   return commit('CHANGE',state)
  // }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
}