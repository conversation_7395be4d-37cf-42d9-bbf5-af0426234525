<template>
  <div class="home-editor-body">
    <editor-view v-for="model in list" :model="model"> </editor-view>

    <div class="posBox" v-show="this.$store.state.admin.isSpecial && !$store.state.admin.pid">
      <div class="backround">
        <vue-seamless-scroll :data="textList" :class-option="optionHover" class="seamless-warp">
          <p v-for="(v, i) in textList" :key="i" class="topText">
            <span class="nameText">{{ v.title }}</span>
            <span class="timeText">{{ v.alias }}</span>
          </p>
        </vue-seamless-scroll>
      </div>
      <div class="bot">资讯汇聚</div>
      <!-- <img src="http://cdn.zn.nextv.show/img/20230614/34462846/897.png" alt="" class="body0Img" /> -->
    </div>
  </div>
</template>

<script>
import Base from "./body";
import request from "@/api/modules/request.js"
import vueSeamlessScroll from "vue-seamless-scroll";
export default {
  extends: Base,
  data() {
    return {
      status: false,
      list: [],
      textList: []
    };
  },
  components: { vueSeamlessScroll },
  computed: {
    optionHover() {
      return {
        step: 0.4, // 数值越大速度滚动越快
        limitMoveNum: 0, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 0, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  created() {
    if (this.$store.state.admin.isSpecial) {
      if (this.$store.state.admin.pid) {
        this.list = this.model.theme == 0 ? this.model.body :
          [
            {
              name: "video",
              width: 1000,
              height: 562,
              x: 0,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            {
              name: "rect",
              width: 770,
              height: 266,
              x: 1030,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "",
                  width: 770,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            //  {
            //    name: "rect1",
            //    width: 770,
            //    height: 266,
            //    x: 1030,
            //    y: 294,
            //    r: 0,
            //    shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //    border: {
            //      enabled: 1,
            //      width: 0,
            //      style: "none",
            //      color: "none",
            //      radius: 10,
            //    },
            //    background: {
            //      color: "rgba(0,0,0,0.2)",
            //      opacity: 0.2,
            //      image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
            //      size: "cover",
            //    },
            //    children: [
            //      {
            //        name: "label",
            //        // value: "资讯汇聚",
            //        width: 770,
            //        height: 50,
            //        x: 0,
            //        y: 216,
            //        r: 0,
            //        font: {
            //          size: 20,
            //          color: "#fff",
            //          weight: 500,
            //          height: 50,
            //          align: "center",
            //        },
            //        shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //        border: {
            //          width: 0,
            //          style: "none",
            //          color: "none",
            //          radius: 10,
            //        },
            //        background: {
            //          color: "rgba(0,0,0,0.2)",
            //        opacity: 0.2,
            //        image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
            //        size: "cover",
            //        },
            //      }
            //    ],
            //  },
            {
              name: "rect",
              width: 770,
              height: 266,
              x: 1030,
              y: 294,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "",
                  width: 770,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
          ]
      } else {

        this.list = this.model.theme == 0 ? this.model.body :
          [
            {
              name: "video",
              width: 1000,
              height: 562,
              x: 0,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            {
              name: "rect",
              width: 770,
              height: 266,
              x: 1030,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "",
                  width: 770,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect1",
              width: 770,
              height: 266,
              x: 1030,
              y: 294,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                // color: "rgba(0,0,0,0.2)",
                // opacity: 0.2,
                image: "http://cdn.zn.nextv.show/img/20230614/34462846/897.png",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  // value: "资讯汇聚",
                  width: 770,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    // color: "rgba(0,0,0,0.2)",
                    // opacity: 0.2,
                    image: "http://cdn.zn.nextv.show/img/20230614/34462846/897.png",
                    size: "cover",
                  },
                }
              ],
            },
            // {
            //   name: "rect",
            //   width: 770,
            //   height: 266,
            //   x: 1030,
            //   y: 294,
            //   r: 0,
            //   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //   border: {
            //     enabled: 1,
            //     width: 0,
            //     style: "none",
            //     color: "none",
            //     radius: 10,
            //   },
            //   background: {
            //     color: "rgba(0,0,0,0.2)",
            //     image: "",
            //     size: "cover",
            //   },
            //   children: [
            //     {
            //       name: "label",
            //       value: "",
            //       width: 770,
            //       height: 50,
            //       x: 0,
            //       y: 216,
            //       r: 0,
            //       font: {
            //         size: 20,
            //         color: "#fff",
            //         weight: 500,
            //         height: 50,
            //         align: "center",
            //       },
            //       shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //       border: {
            //         width: 0,
            //         style: "none",
            //         color: "none",
            //         radius: 10,
            //       },
            //       background: {
            //         color: "rgba(0,0,0,0.4)",
            //         image: "",
            //         size: "cover",
            //       },
            //     },
            //   ],
            // },
          ]
      }
    } else {
      this.list = this.model.theme == 0 ? this.model.body :
        [
          {
            name: "video",
            width: 1000,
            height: 562,
            x: 0,
            y: 0,
            r: 0,
            loop: 1,
            address: "",
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              image: "",
              size: "cover",
            },
          },
          {
            name: "rect",
            width: 770,
            height: 266,
            x: 1030,
            y: 0,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              image: "",
              size: "cover",
            },
            children: [
              {
                name: "label",
                value: "",
                width: 770,
                height: 50,
                x: 0,
                y: 216,
                r: 0,
                font: {
                  size: 20,
                  color: "#fff",
                  weight: 500,
                  height: 50,
                  align: "center",
                },
                shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                border: {
                  width: 0,
                  style: "none",
                  color: "none",
                  radius: 10,
                },
                background: {
                  color: "rgba(0,0,0,0.4)",
                  image: "",
                  size: "cover",
                },
              },
            ],
          },
          //  {
          //    name: "rect1",
          //    width: 770,
          //    height: 266,
          //    x: 1030,
          //    y: 294,
          //    r: 0,
          //    shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          //    border: {
          //      enabled: 1,
          //      width: 0,
          //      style: "none",
          //      color: "none",
          //      radius: 10,
          //    },
          //    background: {
          //      color: "rgba(0,0,0,0.2)",
          //      opacity: 0.2,
          //      image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
          //      size: "cover",
          //    },
          //    children: [
          //      {
          //        name: "label",
          //        // value: "资讯汇聚",
          //        width: 770,
          //        height: 50,
          //        x: 0,
          //        y: 216,
          //        r: 0,
          //        font: {
          //          size: 20,
          //          color: "#fff",
          //          weight: 500,
          //          height: 50,
          //          align: "center",
          //        },
          //        shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          //        border: {
          //          width: 0,
          //          style: "none",
          //          color: "none",
          //          radius: 10,
          //        },
          //        background: {
          //          color: "rgba(0,0,0,0.2)",
          //        opacity: 0.2,
          //        image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
          //        size: "cover",
          //        },
          //      }
          //    ],
          //  },
          {
            name: "rect",
            width: 770,
            height: 266,
            x: 1030,
            y: 294,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              image: "",
              size: "cover",
            },
            children: [
              {
                name: "label",
                value: "",
                width: 770,
                height: 50,
                x: 0,
                y: 216,
                r: 0,
                font: {
                  size: 20,
                  color: "#fff",
                  weight: 500,
                  height: 50,
                  align: "center",
                },
                shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                border: {
                  width: 0,
                  style: "none",
                  color: "none",
                  radius: 10,
                },
                background: {
                  color: "rgba(0,0,0,0.4)",
                  image: "",
                  size: "cover",
                },
              },
            ],
          },
        ]
    }
    //     this.list = [
    //         {
    //             name: "video",
    //             width: 1000,
    //             height: 562,
    //             x: 0,
    //             y: 0,
    //             r: 0,
    //             loop: 1,
    //             address: "",
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               enabled: 1,
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.2)",
    //               image: "",
    //               size: "cover",
    //             },
    //           },
    //           {
    //             name: "rect",
    //             width: 770,
    //             height: 266,
    //             x: 1030,
    //             y: 0,
    //             r: 0,
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               enabled: 1,
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.2)",
    //               image: "",
    //               size: "cover",
    //             },
    //             children: [
    //               {
    //                 name: "label",
    //                 value: "",
    //                 width: 770,
    //                 height: 50,
    //                 x: 0,
    //                 y: 216,
    //                 r: 0,
    //                 font: {
    //                   size: 20,
    //                   color: "#fff",
    //                   weight: 500,
    //                   height: 50,
    //                   align: "center",
    //                 },
    //                 shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //                 border: {
    //                   width: 0,
    //                   style: "none",
    //                   color: "none",
    //                   radius: 10,
    //                 },
    //                 background: {
    //                   color: "rgba(0,0,0,0.4)",
    //                   image: "",
    //                   size: "cover",
    //                 },
    //               },
    //             ],
    //           },
    //           //  {
    //           //    name: "rect1",
    //           //    width: 770,
    //           //    height: 266,
    //           //    x: 1030,
    //           //    y: 294,
    //           //    r: 0,
    //           //    shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //           //    border: {
    //           //      enabled: 1,
    //           //      width: 0,
    //           //      style: "none",
    //           //      color: "none",
    //           //      radius: 10,
    //           //    },
    //           //    background: {
    //           //      color: "rgba(0,0,0,0.2)",
    //           //      opacity: 0.2,
    //           //      image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
    //           //      size: "cover",
    //           //    },
    //           //    children: [
    //           //      {
    //           //        name: "label",
    //           //        // value: "资讯汇聚",
    //           //        width: 770,
    //           //        height: 50,
    //           //        x: 0,
    //           //        y: 216,
    //           //        r: 0,
    //           //        font: {
    //           //          size: 20,
    //           //          color: "#fff",
    //           //          weight: 500,
    //           //          height: 50,
    //           //          align: "center",
    //           //        },
    //           //        shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //           //        border: {
    //           //          width: 0,
    //           //          style: "none",
    //           //          color: "none",
    //           //          radius: 10,
    //           //        },
    //           //        background: {
    //           //          color: "rgba(0,0,0,0.2)",
    //           //        opacity: 0.2,
    //           //        image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
    //           //        size: "cover",
    //           //        },
    //           //      }
    //           //    ],
    //           //  },
    //           {
    //             name: "rect",
    //             width: 770,
    //             height: 266,
    //             x: 1030,
    //             y: 294,
    //             r: 0,
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               enabled: 1,
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.2)",
    //               image: "",
    //               size: "cover",
    //             },
    //             children: [
    //               {
    //                 name: "label",
    //                 value: "",
    //                 width: 770,
    //                 height: 50,
    //                 x: 0,
    //                 y: 216,
    //                 r: 0,
    //                 font: {
    //                   size: 20,
    //                   color: "#fff",
    //                   weight: 500,
    //                   height: 50,
    //                   align: "center",
    //                 },
    //                 shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //                 border: {
    //                   width: 0,
    //                   style: "none",
    //                   color: "none",
    //                   radius: 10,
    //                 },
    //                 background: {
    //                   color: "rgba(0,0,0,0.4)",
    //                   image: "",
    //                   size: "cover",
    //                 },
    //               },
    //             ],
    //           },
    //        ]
    //        break;
    //     case true : 
    //        this.list =  [
    //             {
    //               name: "video",
    //               width: 1000,
    //               height: 562,
    //               x: 0,
    //               y: 0,
    //               r: 0,
    //               loop: 1,
    //               address: "",
    //               shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //               border: {
    //                 enabled: 1,
    //                 width: 0,
    //                 style: "none",
    //                 color: "none",
    //                 radius: 10,
    //               },
    //               background: {
    //                 color: "rgba(0,0,0,0.2)",
    //                 image: "",
    //                 size: "cover",
    //               },
    //             },
    //             {
    //               name: "rect",
    //               width: 770,
    //               height: 266,
    //               x: 1030,
    //               y: 0,
    //               r: 0,
    //               shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //               border: {
    //                 enabled: 1,
    //                 width: 0,
    //                 style: "none",
    //                 color: "none",
    //                 radius: 10,
    //               },
    //               background: {
    //                 color: "rgba(0,0,0,0.2)",
    //                 image: "",
    //                 size: "cover",
    //               },
    //               children: [
    //                 {
    //                   name: "label",
    //                   value: "",
    //                   width: 770,
    //                   height: 50,
    //                   x: 0,
    //                   y: 216,
    //                   r: 0,
    //                   font: {
    //                     size: 20,
    //                     color: "#fff",
    //                     weight: 500,
    //                     height: 50,
    //                     align: "center",
    //                   },
    //                   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //                   border: {
    //                     width: 0,
    //                     style: "none",
    //                     color: "none",
    //                     radius: 10,
    //                   },
    //                   background: {
    //                     color: "rgba(0,0,0,0.4)",
    //                     image: "",
    //                     size: "cover",
    //                   },
    //                 },
    //               ],
    //             },
    //             {
    //               name: "rect1",
    //               width: 770,
    //               height: 266,
    //               x: 1030,
    //               y: 294,
    //               r: 0,
    //               shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //               border: {
    //                 enabled: 1,
    //                 width: 0,
    //                 style: "none",
    //                 color: "none",
    //                 radius: 10,
    //               },
    //               background: {
    //                 color: "rgba(0,0,0,0.2)",
    //                 opacity: 0.2,
    //                 image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
    //                 size: "cover",
    //               },
    //               children: [
    //                 {
    //                   name: "label",
    //                   // value: "资讯汇聚",
    //                   width: 770,
    //                   height: 50,
    //                   x: 0,
    //                   y: 216,
    //                   r: 0,
    //                   font: {
    //                     size: 20,
    //                     color: "#fff",
    //                     weight: 500,
    //                     height: 50,
    //                     align: "center",
    //                   },
    //                   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //                   border: {
    //                     width: 0,
    //                     style: "none",
    //                     color: "none",
    //                     radius: 10,
    //                   },
    //                   background: {
    //                     color: "rgba(0,0,0,0.2)",
    //                   opacity: 0.2,
    //                   image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
    //                   size: "cover",
    //                   },
    //                 }
    //               ],
    //             },
    //             // {
    //             //   name: "rect",
    //             //   width: 770,
    //             //   height: 266,
    //             //   x: 1030,
    //             //   y: 294,
    //             //   r: 0,
    //             //   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             //   border: {
    //             //     enabled: 1,
    //             //     width: 0,
    //             //     style: "none",
    //             //     color: "none",
    //             //     radius: 10,
    //             //   },
    //             //   background: {
    //             //     color: "rgba(0,0,0,0.2)",
    //             //     image: "",
    //             //     size: "cover",
    //             //   },
    //             //   children: [
    //             //     {
    //             //       name: "label",
    //             //       value: "",
    //             //       width: 770,
    //             //       height: 50,
    //             //       x: 0,
    //             //       y: 216,
    //             //       r: 0,
    //             //       font: {
    //             //         size: 20,
    //             //         color: "#fff",
    //             //         weight: 500,
    //             //         height: 50,
    //             //         align: "center",
    //             //       },
    //             //       shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             //       border: {
    //             //         width: 0,
    //             //         style: "none",
    //             //         color: "none",
    //             //         radius: 10,
    //             //       },
    //             //       background: {
    //             //         color: "rgba(0,0,0,0.4)",
    //             //         image: "",
    //             //         size: "cover",
    //             //       },
    //             //     },
    //             //   ],
    //             // },
    //           ] 

    // }
    // this.list =
    //   this.model.theme == 0
    //     ? this.model.body
    //     : (
    //       this.$store.state.admin.pid == 0 ?

    //        : 

    //     );


    if (!this.$store.state.admin.pid) {
      request.get('/v1/media.item.subList', { creator: this.$store.state.admin.id }).then((res) => {
        console.log(res);
        this.textList = res
      }).catch(err => {
        console.log(err);
      })
    } else {
      request.get('/v1/media.item.pList').then((res) => {
        console.log(res);
        this.textList = res
      }).catch(err => {
        console.log(err);
      })
    }
  },
};
</script>


<style lang="scss">
.home-editor-bod {
  position: relative;
}

.posBox {
  width: 770px;
  height: 266px;
  position: fixed;
  top: 482px;
  right: 60px;
  border-radius: 10px;
}

.img {
  width: 770px;
  height: 266px;

  opacity: 0.3;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
}

::-webkit-scrollbar {
  display: none;
}

.topText {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-around;
  padding: 0 20px;
  line-height: 40px;
  z-index: 99;
}

.nameText {
  width: 60%;
  height: 100%;
  color: #fdf451;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: left;
}

.titleText {
  flex: 1;
  text-align: center;
  color: #f1f1f1;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: center;
}

.timeText {
  width: 30%;
  height: 100%;
  color: white;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: right;
}

.bot {
  width: 100%;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 10px 10px;
  font-size: 25px;
  color: white;
  text-align: center;
  line-height: 50px;
  z-index: 99;
}

.backround {
  width: 770px;
  height: 266px;
  border-radius: 10px;
  overflow: auto;
  background: url(http://cdn.zn.nextv.show/img/20230614/35151357/962.png) no-repeat;
  background-size: 100% 100%;
}

.seamless-warp {
  height: 266px;
  overflow: hidden;
}

.body0Img {
  width: 770px;
  height: 266px;
  opacity: 0.7;
  // background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
}
</style>
