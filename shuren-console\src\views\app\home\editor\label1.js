import Base from "./base";

export default {
  name: "EditorLabel1",
  extends: Base,
  mounted() {
    this.$el.innerText = this.model.value;
  },
  methods: {
    onBlur() {
      this.model.value = this.$el.innerText;
    },
    onPaste(e) {
      e.stopPropagation();
      e.preventDefault();

      let text = e.clipboardData.getData("Text");
      let node = document.createTextNode(text);

      let selection = window.getSelection();
      if (!selection.rangeCount) return false;

      let range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(node);
      range = range.cloneRange();
      range.setStartAfter(node);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    },
    onKeypress(e) {
      if (e.keyCode == 13) {
        e.stopPropagation();
        e.preventDefault();
        this.$el.blur();
      }
    },
  },
  render(h) {
    let style = this.getStyle() + this.fontStyle();
    return (
      <div
        class="editor-com editor-label"
        contenteditable="true"
        style={style}
        onblur={this.onBlur}
        onpaste={this.onPaste}
        onkeypress={this.onKeypress}
        placeholder={this.placeholder}
      ></div>
    );
  },
};
