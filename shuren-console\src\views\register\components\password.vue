<template>
  <div class="login-container">
    <div class="login-header">
      <div class="login-type">
        <label class="login-label active">注册账号</label>
      </div>
    </div>
    <el-form class="login-body" ref="form" :model="model" :rules="rules" label-width="104px" label-suffix="：" @submit.native.prevent="submit">
      <el-form-item label="组织名称" prop="company_name">
        <el-input v-model.trim="model.company_name" placeholder="请输入" autofocus="true" autocomplete="off" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item label="登录账号" prop="username">
        <el-popover
            placement="top"
            width="460"
            trigger="hover">
          <div>·5-32个字符，只支持数字、字母，必须包含字母</div>
          <div>·请勿包含身份证/银行卡等隐私信息，一旦设置成功无法修改</div>
          <el-input slot="reference" v-model.trim="model.username" placeholder="请输入" autofocus="true" autocomplete="off" maxlength="32"></el-input>
        </el-popover>
      </el-form-item>
      <el-form-item label="登录密码" prop="password">
        <el-popover
            placement="bottom"
            width="460"
            trigger="hover">
          <div>·6-32个字符，不能和账号重复</div>
          <div slot="reference">
            <el-input v-model="model.password" :type="showPwd ? 'text' : 'password'" placeholder="请输入" autocomplete="off" maxlength="32"></el-input>
            <div class="toggle-pwd">
              <svg slot="suffix" class="icon" @click="showPwd=!showPwd">
                <use :xlink:href="`#${showPwd ? 'show' : 'hide'}-pwd`"></use>
              </svg>
            </div>
          </div>
        </el-popover>
      </el-form-item>
      <el-button class="login-button" type="primary" native-type="submit" :disabled="!agree || loading">注 册</el-button>
    </el-form>
    <div class="login-footer">
      <agree v-model="agree"></agree>
      <div class="float-right">
        <span style="color:#969799">已有账号？</span>
        <router-link :to.replace="login">登录</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import Agree from './agree';

export default {
  components: {Agree},
  props: ['loading'],
  data() {
    return {
      showPwd: false,
      agree: !!localStorage.getItem('AgreeLogin'),
      model: {
        by: 'company',
        company_name: '',
        username: '',
        password: '',
        keep_login: 3
      },
      rules: {
        company_name: {required: true, message: '请输入', trigger: 'change'},
        username: [
          {required: true, message: '请输入', trigger: 'change'},
          {min: 5, max: 32, message: '长度在 5 到 32 个字符', trigger: 'change'}
        ],
        password: [
          {required: true, message: '请输入', trigger: 'change'},
          {min: 6, max: 32, message: '长度在 6 到 32 个字符', trigger: 'change'}
        ]
      }
    }
  },
  computed: {
    login() {
      return {name: 'login', query: {redirect: this.$route.query.redirect}}
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        let model = Object.assign({}, this.model, {password: this.$utils.md5(this.model.password)});

        this.$emit('submit', model);
      });
    }
  }
}
</script>