import Env from '@/utils/env';
import request from '@/utils/request';

function syncLocal(data) {
  if (data) {
    localStorage.setItem('admin', JSON.stringify(data));
  } else {
    localStorage.removeItem('admin');
  }
}
const state = {
  status: -1,
  id: null,
  name: '',
  username: '',
  nickname: '',
  headimg: '',
  logo: '',
  copyright: '',
  company_id: 0,
  pid:0,
  sub_Users:[],
  radios:1,
  isSpecial:false,
  operation:false,
  endTime:""
}

let his = localStorage.getItem('admin');
if (his) {
  try {
    his = JSON.parse(his);
    Object.assign(state, his);
  } catch (e) {
    Env.token = null;
  }
}

const mutations = {
  SET_LOGIN(state, res) {
    state.status = res.status;
    state.id = res.id;
    state.name = res.name;
    state.username = res.username;
    state.nickname = res.nickname;
    state.headimg = res.headimg;
    state.company_id = res.company_id;
    state.pid = res.pid
    state.isSpecial = res.isSpecial
    syncLocal(state);
  },
  sub_UserM(state,paylod){
    state.sub_Users = paylod
  },
  changeRadioM(state,paylod){
    state.radios = paylod
  },
  // 查验当前账户是否是  平台运营账户
  changeOperation(state,payload){
    state.operation = payload
  },
  // changeEndTime(state,payload){
  //   state.endTime = payload

  // }

};

const actions = {
  // endTime({commit},payload){
  //   commit('changeEndTime',payload)
  // },

  // 查验当前账户是否是  平台运营账户
  operation({commit},payload){
    commit('changeOperation',payload)
  },
  
  sub_User({commit},model){
    // console.log(model);
    commit('sub_UserM', model);
  },
  changeRadio({commit},payload){
    commit("changeRadioM",payload)
  },
  login({commit}, model) {
    return request.post('v1/user.account.login', model).then(function (res) {
      // console.log(res);
      commit('SET_LOGIN', res);

      let {opener} = window;
      if (opener && typeof opener.LoginSuccess == 'function') {
        opener.LoginSuccess(res);
        window.close();
      }
      return res;
    });
  },
  register({commit}, model) {
    console.log(model);
    return request.post('v1/user.account.register', model).then(function (res) {
      // let {opener} = window;
      // if (opener && typeof opener.LoginSuccess == 'function') {
      //   opener.LoginSuccess(res);
      //   window.close();
      // } else {
      //   commit('SET_LOGIN', res);
      // }
      // return res;
    });
  },
  checkLogin({commit, dispatch}) {
    return request.post('v1/user.account.check').then(function (res) {
      if (res.status !== -1) {
        commit('SET_LOGIN', res);
      } else {
        dispatch('loginOut');
      }
      return res;
    }).catch(() => {
      dispatch('admin/loginOut');
    });
  },
  logout({state}) {
    state.status = -1;
    state.id = null;
    Env.token = null;
    location.replace('/login');
  },
  headimg({state}, url) {
    return request.post('/v1/user.account.headimg', {url: url}).then(function () {
      state.headimg = url;
      syncLocal(state);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
}