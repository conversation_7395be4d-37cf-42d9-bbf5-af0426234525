<template>
    <div>
      <mytable
        ref="table"
        :list="list"
        :api="$api.media.channel.getNotice"
        :query="query"
        :pagination="false"
      >
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <template>
                <el-button type="primary" size="mini" @click="onEdit"
                  >创建</el-button
                >
              </template>
            </div>
          </div>
        </div>
  
        <el-table-column label="标题" prop="notice_title"></el-table-column>
        <el-table-column label="编号" prop="id"></el-table-column>
        <el-table-column
          label="创建时间"
          prop="created"
          :formatter="fCreate"
          align="center"
        ></el-table-column>
        <el-table-column
          label="发布时间"
          prop="pubdate"
          :formatter="fCreate"
          align="center"
        ></el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          align="center"
        >
        <template scope="scope">
          <span v-if="scope.row.status=== '0'">待发布</span>
          <span v-else-if="scope.row.status=== '1'">已发布</span>
        </template>
      </el-table-column>
        <!-- <el-table-column
          label="显示排序"
          v-if="showSort"
          prop="sort"
          align="center"
          width="90"
          class-name="is-sort"
          :formatter="fSort"
        >
          <input
            slot-scope="scope"
            v-model="sortList[scope.row.id]"
            type="text"
          />
        </el-table-column> -->
        <el-table-column  label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <!-- <span v-if="!platform && scope.row.platform">平台</span> -->
            <template>
                <el-button
                size="mini"
                @click="onPublish(scope.row)"
              >
              发布
              </el-button>
              <el-button
                class="btn-action"
                size="mini"
                @click="onEdit(scope.row)"
                title="编辑"
              >
                <svg class="icon" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xlink:href="#edit"
                  ></use>
                </svg>
              </el-button>
              <el-button
                class="btn-action"
                size="mini"
                @click="onDel(scope.row)"
                title="删除"
              >
                <svg class="icon" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xlink:href="#delete"
                  ></use>
                </svg>
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>
  

      <el-dialog
        :title="model.id ? '编辑通知': '创建通知'"
        :visible.sync="dialogVisible"
        width="500px"
        class="edit-staff-group-dialog"
        @closed="onClosed"
        :append-to-body="true">
  
      <el-form ref="form" label-width="80px" :model="model" :rules="rules">
        <el-form-item label="标题" prop="notice_title">
          <el-input v-model.trim="model.notice_title" placeholder="必填项" @input="change($event)"></el-input>
        </el-form-item>
        <el-form-item label="正文" prop="notice_content">
          <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              placeholder="请输入正文"
              v-model="model.notice_content"
              @input="change($event)">
            </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="model.remark" @input="change($event)"  placeholder=""></el-input>
        </el-form-item>
      </el-form>
  
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-if="!model.id" @click="onSubmit">保 存</el-button>
        <el-button type="primary" v-else  @click="onUpdate">确认修改</el-button>
      </div>
    </el-dialog>

    <el-dialog
        title="发布通知"
        :visible.sync="dialogVisiblePublish"
        width="500px"
        class="edit-staff-group-dialog"
        @closed="onClosed"
        :append-to-body="true"
    >
    <div class="please">请选择下属部门：</div> 
            <el-checkbox-group v-model="checkList">
                <el-checkbox v-for="item in subUsers" :label="item.id" :key="item.id">{{item.nickname}}</el-checkbox>
            </el-checkbox-group>
            <div class="btns">
              <el-button @click="dialogVisiblePublish = false">取 消</el-button>
              <el-button type="primary"  @click="publishNotice">发 布</el-button>
            </div>
    </el-dialog>
    </div>
  </template>
  
  <script>
  
  export default {
    data() {
      return {
        editing: true,
        dialogVisible: false,
        dialogVisiblePublish:false,
        model: {
            id:null,
            notice_title: '',
            notice_content: '',
            remark: ""
        },
        showSort: false,
        sortList: null,
        checkList:[],
        subUsers:[],
        publishModel:{
            userIds:[],
            id:0,
        }
      };
    },
    watch:{
      dialogVisiblePublish(newV,oldV){
          console.log(newV,oldV);
          if(newV){
            this.$api.media.subUsersList().then(res=>{
            this.subUsers = res
          })
          }
      }
    },
    computed: {
      list() {
        return [];
      },
    },
    methods: {
      refresh() {
        this.$refs.table.refresh();
      },
      fType(row, column, val) {
        return this.$api.media.channel.type(val);
      },
      fCreate(row, column, val) {
        return this.$utils.Date(val).format("YYYY-MM-DD");
      },
      onEdit(row) {
        this.model = row;
        this.dialogVisible = true;
      },
      onDel(row) {
        this.$confirm("操作不可恢复，确定删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return this.$api.media.channel.delNotice(row.id);
          })
          .then(this.refresh);
      },
      onShowSort() {
        let res = {};
        function eachList(list) {
          list.forEach((item) => {
            res[item.id] = item.sort;
            if (item.children) eachList(item.children);
          });
        }
  
        eachList(this.$refs.table.list);
  
        this.sortList = res;
        this.showSort = true;
      },
    onClosed() {
      this.dialogVisible = false
      this.dialogVisiblePublish = false
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        this.dialogVisible = false;

        let {model} = this;
        this.$api.post('/v1/sys.notice.create', model).then(res => {
          console.log(res);
        }).then(this.refresh);
      });
    },
    onUpdate(){
        this.$api.post('/v1/sys.notice.update', this.model).then(res => {
          console.log(res);
          this.dialogVisible = false;
        }).then(this.refresh);
    },
    change(e){
        this.$forceUpdate(e)
    },
    onPublish(row){
        this.publishModel.id = row.id
        this.dialogVisiblePublish = true
    },
    publishNotice(){
        this.publishModel.userIds = this.checkList
        this.$api.post("/v1/sys.notice.publish",this.publishModel).then(res=>{
            console.log(res);
            this.dialogVisiblePublish = false
        }).then(this.refresh);
    },


    
    },
  };
  </script>
  <style scoped>
.please{
  line-height: 50px;
  height: 50px;
  width: 100%;
}
.btns{
  margin-top: 50px;
  width: 100%;
  text-align: right;
}
.zanwu{
  width: 100%;
  text-align: center;
}
</style>