import request from './request';

export default {
  captcha(data) {
    return request.post('staff.captcha', data)
  },
  query(params) {
    return request.get('staff.query', params)
  },
  exists(username) {
    return request.get('staff.exists?username=' + username)
  },
  officeStatus() {
    return [{key: 0, val: '待确认'}, {key: 1, val: '履行中'}, {key: 2, val: '已停职'}, {key: 3, val: '已调离'}]
  },
  bind(data) {
    return request.post('staff.bind', data)
  },
  password(data) {
    return request.post('staff.password', data)
  },
  get(id) {
    return request.get('staff.get?id=' + id);
  },
  delete(id) {
    return request.post('staff.delete', {id: id});
  }
};
