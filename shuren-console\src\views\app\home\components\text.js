export default {
  props: {
    value: {
      type: String,
      default: '',
    },
    maxlength: {
      type: Number,
      default: 20,
    },
    placeholder: {
      type: String,
      default: '请输入文本内容',
    },
  },
  watch: {
    value(v) {
      this.$el.innerText = v
    },
  },
  mounted() {
    this.$el.innerText = this.value
  },
  methods: {
    onBlur() {
      this.$emit('input', this.$el.innerText.substr(0, this.maxlength))
    },
    onPaste(e) {
      e.stopPropagation()
      e.preventDefault()

      let text = e.clipboardData.getData('Text')
      let node = document.createTextNode(text)

      let selection = window.getSelection()
      if (!selection.rangeCount) return false

      let range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(node)

      range = range.cloneRange()
      range.setStartAfter(node)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    },
    onKeypress(e) {
      if (e.keyCode == 13) {
        e.stopPropagation()
        e.preventDefault()
        this.$el.blur()
      }
    },
  },
  render() {
    // if (this.$store.state.admin.pid) {
    //   return <div class="home-editor-text" contenteditable="false"></div>
    // } else {
    return <div class="home-editor-text" contenteditable="true" onblur={this.onBlur} onpaste={this.onPaste} onkeypress={this.onKeypress} placeholder={this.placeholder}></div>
    // }
  },
}
