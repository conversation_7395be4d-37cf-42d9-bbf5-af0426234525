<template>
  <el-dialog
      title="负责人" :visible.sync="visible" width="450px" @closed="closed" v-if="model"
      :append-to-body="true" :close-on-press-escape="false" :close-on-click-modal="false" custom-class="shop-leader-dialog">
    <el-form label-width="80px" size="small" style="padding-right:50px">
      <el-form-item label="称 呼">
        <el-input v-model="model.leader_name" maxlenght="20"></el-input>
      </el-form-item>
      <el-form-item label="电 话">
        <el-input v-model="model.leader_mobile" maxlenght="20"></el-input>
      </el-form-item>
      <el-form-item label="微 信">
        <el-input v-model="model.leader_wechat" maxlenght="32"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="submit" size="small">保 存</el-button>
      <el-button type="primary" @click="visible = false" size="small">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    props: ['value'],
    data() {
      let {value} = this;

      return {
        visible: 0,
        model: null
      }
    },
    watch: {
      value(val) {
        if (val) {
          this.visible = 1;
          this.model = {id: val.id, leader_name: val.leader_name, leader_mobile: val.leader_mobile, leader_wechat: val.leader_wechat};
        } else {
          this.visible = 0;
        }
      }
    },
    methods: {
      closed() {
        this.$emit('input', null);
      },
      submit() {
        let {model} = this;
        this.$api.shop.setLeader(model).then(_ => {
          Object.assign(this.value, model);
          this.visible = false;
        });
      }
    }
  }
</script>