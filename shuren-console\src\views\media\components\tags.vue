<template>
  <el-select
      style="display:block"
      size="mini"
      :popper-append-to-body="false"
      popper-class="is-group"
      v-model="tags"
      multiple
      :multiple-limit="3"
      filterable
      clearable
      default-first-option
      placeholder="标签"
      @change="tagChanged">
    <el-option-group label="">
      <el-option
          v-for="item in allTags"
          :key="item.id"
          :label="item.label"
          :value="item.id"
          :disabled="item.disabled">
      </el-option>
    </el-option-group>
    <el-option-group label="自定义">
      <el-option
          v-for="item in keywords"
          :key="item.id"
          :label="item.label"
          :value="item.id">
      </el-option>
      <el-option label="自定义" value="-1" :disabled="true">
        <input v-model="keyword" placeholder="请输入" maxlength="5" style="width:100%;" @blur="addKeyWord" @keypress="addKeyWord">
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
  export default {
    props: ['value'],
    data(){
      return {
        tags: this.value ? this.value.split(',') : [],
        allTags: [],
        keyword: '',
        keywords: []
      }
    },
    watch: {
      value(v){
        if(v != this.tags.join(',')){
          this.tags = v ? v.split(',') : []
        }
      }
    },
    methods: {
      syncTags(){
        API.media.tag.list().then(list => {
          this.allTags = list;
        });
      },
      tagChanged(){
        this.$emit('input', this.tags.join(','));
      },
      addKeyWord(e){
        if(this.tags.length >= this.limit || e.type != 'blur' && (e.type == 'keypress' && e.key != 'Enter')){
          return;
        }

        let kw = this.keyword.replace(/ /g, ''), len = kw.length;
        if(len == 0){
          return;
        }

        if(len < 2){
          return this.$notify.message('最少2个字');
        }

        this.keyword = '';

        // 判断是否已存在tags中
        if(this.existsTag(this.allTags, kw)){
          return;
        }

        // 判断是否已存在keywords中
        let keywords = this.keywords;
        if(this.existsTag(this.allTags, kw)){
          return;
        }

        API.media.tag.add({label: kw}).then(res => {
          keywords.push(res);

          this.$nextTick(() => {
            this.tags.push(res.id);
            this.tagChanged();
          });
        });
      },
      existsTag(list, label){
        for(let i=0; i<list.length; i++){
          if(list[i].label == label){
            let id = list[i].id;
            if(this.tags.indexOf(id) == -1){
              this.tags.push(id);
            }
            return true;
          }
        }
      }
    },
    created(){
      this.syncTags();
    }
  }
</script>