<template>
    <div>
      <mytable ref="table" :list="list" :api="$api.integral.list" :query="query" :pagination="false"
               @rowClick="clickRow">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <template>
                状态：<el-select v-model="value" placeholder="请选择" size="mini" @change="selectChange">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </template>
            </div>
          </div>
        </div>
  
        <el-table-column label="商品" align="center" prop="goods_name" width="400"></el-table-column>
        <el-table-column label="数量" prop="goods_num" width="90"></el-table-column>
        <el-table-column label="积分" prop="goods_integral" width="90"></el-table-column>
        <el-table-column label="姓名" prop="name" align="center" width="100"></el-table-column>
        <el-table-column label="电话" prop="mobile" align="center"></el-table-column>
        <el-table-column label="兑换时间" prop="exchange_time" align="center" width="200">
            <template slot-scope="scope">
                {{ $utils.Date(scope.row.exchange_time).toString() }}
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" v-if="scope.row.status == 1" @click.stop="onEdit(scope.row)" title="兑换">
                兑换
              </el-button>
              <el-button class="btn-action" size="mini" v-if="scope.row.status == 2" @click.stop="onDel(scope.row)" title="删除">
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>
    </div>
  </template>
    
  <script>
  export default {
    data() {
      return {
        query:{
            status:1
        },
        value:'未兑换',
        options:[
        {
            value: '2',
            label: '已兑换',
        },
        {
            value: '1',
            label: '未兑换',
        },
        ]
      };
    },
    computed: {
      list() {
        return [];
      },
    },
    methods: {
    
      refresh() {
        this.$refs.table.refresh();
      },
      onEdit(row) {
        
        this.$confirm("操作不可恢复，确定用户已完成兑换吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = row
            row.status = 2
            row.exchange_time = parseInt(new Date().getTime() / 1000) 
            return this.$api.post('/v1/integral.update', data);
          })
          .then(this.refresh);
      },
      onDel(v){
        this.$confirm("操作不可恢复，确定删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return this.$api.post('/v1/integral.delete', {id:v.id});
          })
          .then(this.refresh);
      },
      selectChange(v){
        console.log(v);
        this.$refs.table.refresh({ status:v})
      }
  
    },
    beforeDestroy() {
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  .zanwu {
    width: 100%;
    text-align: center;
  }
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  .poster {
    width: 100px;
    height: 100px;
    border: 1px solid black;
    cursor: pointer;
    text-align: center;
  }
  
  .posterImg {
    width: 150px;
    height: 100px;
    position: absolute;
    top: 0px;
    cursor: pointer;
  }
  </style>