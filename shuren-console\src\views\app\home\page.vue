<template>
  <div class="home-editor-page">
    <div ref="monitor" class="home-editor-monitor">
      <!-- 设置按钮  -->
      <tool-button></tool-button>

      <div ref="container" class="home-editor-container" :style="style">
        <template v-if="!loading">
          <!-- LOGO标题 -->
          <div class="home-editor-header">
            <text-label class="home-editor-title" v-model="model.title" maxlength="32"></text-label>
          </div>
          <keep-alive>
            <component :is="`body-${theme}`"></component>
          </keep-alive>
          <!-- 6个图标导航 -->
          <div class="home-editor-footer">
            <div class="home-editor-menu-list">
              <app-menu-item v-for="menu in model.menus" :model="menu"></app-menu-item>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.home-editor-monitor {
  background: #000;
  border: 30px solid #000;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.home-editor-container {
  width: 1920px;
  height: 1080px;
  background: red url("https://cdn.nextv.show/app/icon/8.jpg") no-repeat 50% 50% / cover;
  padding: 60px 60px;
  transform-origin: 0 0;
  user-select: none;
  transition: background 0.6s;
}

.home-editor-title {
  // color: #fff400;
  font-size: 70px;
  line-height: 1;
  text-align: center;
  font-weight: bold;
  // text-shadow: rgb(0, 0, 0) 0 0 20px;
  letter-spacing: 12px;
  background-image: linear-gradient(0deg, #f2ca51, #fefdf9) !important;
  background-clip: text !important;
  color: transparent !important;
}

.home-editor-menu-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.home-editor-menu-item {
  text-align: center;
}

.home-editor-menu-icon {
  width: 160px;
  height: 160px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 15px;
  filter: drop-shadow(black 0 0 20px);
  transition: background-color 0.6s;

  img {
    display: block;
    // object-fit: cover;
    width: 170px;
    height: 130px;
  }
}

.home-editor-menu-label {
  color: #fff;
  font-size: 28px;
  text-shadow: rgb(0, 0, 0) 0 0 20px;
  margin-top: 20px;
  font-weight: bold;
  line-height: 1;
}

.home-editor-link {
  z-index: 1;
  cursor: pointer;
  color: #fff;
  font-size: 32px;
  position: absolute;
  top: 0;
  right: 0;
  text-shadow: black 0 0 20px;
  filter: drop-shadow(black 0 0 20px);
  padding: 6px;
  display: none;
}

.home-editor-menu-icon:hover,
.home-editor-body-card:hover {
  .home-editor-link {
    display: block;
  }
}

.home-editor-body {
  height: 562px;
  margin: 60px 0;
}

.home-editor-text {
  cursor: text;
  outline: none;
  // text-shadow: rgb(0, 0, 0) 0 0 20px;
  border: none;
  background: none;
  text-align: center;
  width: 100%;

  &:empty:before {
    content: attr(placeholder);
    color: #f0f0f0;
  }
}

.home-editor-play-button {
  z-index: 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 80px;
  color: #fff;
  filter: drop-shadow(black 0 0 20px);
}

.home-editor-tool {
  z-index: 1;
  cursor: pointer;
  position: absolute;
  right: 20px;
  top: 20px;
}

.home-editor-tool-button {
  font-size: 30px;
  filter: drop-shadow(black 0 0 20px);
  animation: home-editor-tool-button 6s linear infinite;
}

@keyframes home-editor-tool-button {
  from {
    color: #fff;
  }

  50% {
    color: #409eff;
  }

  to {
    color: #fff;
  }
}
</style>

<script>
import AppMenuItem from "./components/menu-item";
import TextLabel from "./components/text";
import ToolButton from "./components/tool-button";
import Body0 from "./components/body0";
import Body1 from "./components/body1";
import Body2 from "./components/body2";

export default {
  components: { AppMenuItem, TextLabel, ToolButton, Body0, Body1, Body2 },
  data() {
    const cdn = this.$env.CDN_URL;

    return {
      theme: 0,
      loading: true,
      scale: 0.1,
      model: {
        title: "不忘初心 牢记使命 听党指挥 能打胜仗 作风优良",
        bgimage: cdn + "/app/icon/9.jpg",
        bgcolor: "red",
        grayscale: 0,
        theme: 0,
        menus: [
          {
            title: "组织架构",
            fill: "#f00000",
            icon: cdn + "/app/icon/icon-dept.png",
            name: "dept",
          },
          {
            title: "党章党规",
            fill: "#ffa724",
            icon: cdn + "/app/icon/icon-dzdg.png",
            name: "media.channel",
            params: { id: null },
          },
          {
            title: "党建宣传",
            fill: "#f54337",
            icon: cdn + "/app/icon/icon-djxc.png",
            name: "media.channel",
            params: { id: null },
          },
          {
            title: "学习教育",
            fill: "#ff8000",
            icon: cdn + "/app/icon/icon-xxjy.png",
            name: "media.channel",
            params: { id: null },
          },
          {
            title: "习近平系列讲话",
            fill: "#ff5a25",
            icon: cdn + "/app/icon/icon-xjpxljh.png",
            name: "media.channel",
            params: { id: null },
          },
          {
            title: "系统设置",
            fill: "#1DA0FF",
            icon: cdn + "/app/icon/6.png",
            name: "system.setting",
          },
        ],
        body: [
          {
            name: "video",
            width: 1000,
            height: 562,
            x: 0,
            y: 0,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: { color: "rgba(0,0,0,0.2)", image: "", size: "cover" },
          },
          {
            name: "rect",
            width: 770,
            height: 266,
            x: 1030,
            y: 0,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: { color: "rgba(0,0,0,0.2)", image: "", size: "cover" },
          },
          {
            name: "rect",
            width: 770,
            height: 266,
            x: 1030,
            y: 294,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: { color: "rgba(0,0,0,0.2)", image: "", size: "cover" },
          },
        ],
      },
    };
  },
  computed: {
    style() {
      return `transform: scale(${this.scale});background-color:${this.model.bgcolor};background-image:url(${this.model.bgimage});filter:grayscale(${this.model.grayscale}%)`;
    },
  },
  created() {

    this.loading = true;
    let owner = this.$store.state.admin.id;
    this.$api.get("/v1/app.home.get", { owner: owner }).then((res) => {
      if (res) {
        this.theme = res.theme;
        // res.body.pop();
        // if (this.$store.state.admin.pid) {
        //   this.model = res
        // } else {
        this.model = res
        // }

      } else {
        this.model.owner = owner;
      }
      this.loading = false;
    });
  },
  mounted() {
    // console.log(this.model.menus);
    window.addEventListener("resize", this.resize);
    this.resize();
    this.$el.addEventListener(
      "contextmenu",
      function (e) {
        e.stopPropagation();
        e.preventDefault();
      },
      true
    );
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    resize() {
      let width = this.$el.clientWidth - 40 - 60;
      let height = (width * 9) / 16;
      let maxHeight = document.body.clientHeight - 55 - 80 - 60 - 20;

      if (height > maxHeight) {
        height = maxHeight;
        width = (height * 16) / 9;
      }

      let scale = (height / 1080).toFixed(6);
      this.$refs.monitor.style.cssText =
        "width:" + (width + 60) + "px;height:" + (height + 60) + "px";
      this.scale = scale;
    },
  },
};
</script>