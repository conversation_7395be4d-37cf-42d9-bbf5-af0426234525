<template>
  <div class="edit-base">
    <div class="edit-panel-title">详细介绍</div>
    <div class="edit-panel-desc">建议您使用文字 + 图片即可，复杂的html、iframe将需要联网</div>
    <div class="edit-panel-body">
      <media-editor ref="editor" v-model="model.content" :height="768"></media-editor>
    </div>
  </div>
</template>

<script>
import MediaEditor from '@/views/media/components/editor';

export default {
  components: {MediaEditor},
  data() {
    return {}
  },
  computed: {
    model() {
      return this.$parent.model
    }
  },
  methods: {
    validate(callback) {
      let {editor} = this.$refs;
      let {model} = this;
      model.content = editor.getData();
      if (!model.abstract) {
        model.abstract = editor.getData(true).substr(0, 120).replace(/\s/g, '');
      }
      callback();
    }
  }
}
</script>