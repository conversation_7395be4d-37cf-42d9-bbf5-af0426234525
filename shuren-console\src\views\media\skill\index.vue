<template>
    <div>
      <mytable ref="table" api="/v1/media.skill.list" :list="list">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
            </div>
          </div>
        </div>
        <el-table-column label="编号" prop="id" align="center"></el-table-column>
        <el-table-column label="培训类目" prop="train_type" align="center" width="200"></el-table-column>
        <el-table-column label="培训等级" prop="train_level" align="center"></el-table-column>
        <el-table-column label="培训人数" prop="train_num" align="center"></el-table-column>
        <el-table-column label="报名时间" prop="apply_time" align="center" width="250"></el-table-column>
        <el-table-column label="培训时间" prop="train_time" align="center" width="250"></el-table-column>
        <el-table-column label="培训地点" prop="train_address" align="center" width="400"></el-table-column>
        <el-table-column label="操作" width="90" class-name="table-action">
          <template slot-scope="scope">
            <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
              </svg>
            </el-button>
            <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
              </svg>
            </el-button>
          </template>
        </el-table-column>
      </mytable>


      <el-dialog :title="flag ? '添加培训' : '编辑培训'" :visible.sync="dialogVisible" width="800px" @closed="onClosed"
                 :append-to-body="true" :close-on-click-modal="false">
        <el-form ref="form" label-width="80px" :model="model" :rules="rules">
          <el-form-item label="培训类目" prop="train_type">
            <el-input v-model.trim="model.train_type" placeholder="必填项"></el-input>
          </el-form-item>
          <el-form-item label="培训等级" prop="train_level">
            <el-input v-model.trim="model.train_level" placeholder="必填项"></el-input>
          </el-form-item>
          <el-form-item label="培训人数" prop="train_num">
            <el-input v-model.trim="model.train_num" placeholder="必填项"></el-input>
          </el-form-item>
          <el-form-item label="报名时间" prop="apply_time">
            <el-date-picker type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%;" v-model="model.apply_time" value-format="yyyy年MM月dd日" format="yyyy-MM-dd" ></el-date-picker>
          </el-form-item>
          <el-form-item label="培训时间" prop="train_time">
            <el-date-picker type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%;" v-model="model.train_time" value-format="yyyy年MM月dd日" format="yyyy-MM-dd" ></el-date-picker>
          </el-form-item>
          <el-form-item label="培训地点" prop="train_address">
            <el-input v-model="model.train_address" placeholder="必填项"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="onSubmit">保 存</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  <script>
  import request from "@/api/modules/request.js"
  export default {
    name: 'skill',
    data() {
      return {
        list: [],
        flag: true,
        dialogVisible: false,
        model: {
          train_type: '',
          train_level: '',
          apply_time: '',
          train_num: null,
          train_address:'',
          train_time:null
        },
        person: []
      };
    },
    created() {
      
    },
    mounted() {
    },
    methods: {
      refresh() {
        this.model = {
          train_type: '',
          train_level: '',
          apply_time: '',
          train_num: null,
          train_address:'',
          train_time:null
        }
        this.$refs.table.refresh();
      },
      onAdd() {
        this.flag = true
        this.model.train_type = ''
        this.model.train_level = ''
        this.model.apply_time = null
        this.model.train_num = ''
        this.model.train_address = ''
        this.model.train_time = null
        this.dialogVisible = true
      },
      onSubmit() {
        let time = new Date()
        if (this.flag) {
          // 添加
          this.model.created = parseInt(time.getTime() / 1000) 
          this.model.apply_time = this.model.apply_time.join('至')
          this.model.train_time = this.model.train_time.join('至')
          console.log(this.model);
          request.post('/v1/media.skill.create', this.model).then(res => {
            this.dialogVisible = false
            this.refresh()
          })
        } else {
          // 修改
          this.model.apply_time = this.model.apply_time.join('至')
          this.model.train_time = this.model.train_time.join('至')
          request.post('/v1/media.skill.update', this.model).then(res => {
            this.dialogVisible = false
            this.refresh()
          })
        }
      },
      onEdit(row) {
        this.flag = false
        this.dialogVisible = true
        this.model = row

        // 将字符串时间转换回数组格式
        if (typeof this.model.apply_time === "string") {
          this.model.apply_time = this.model.apply_time.split("至");
        }
        if (typeof this.model.train_time === "string") {
          this.model.train_time = this.model.train_time.split("至");
        }
      },
      onDelete(row) {
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          request.post('/v1/media.skill.delete', { id: row.id }).then(res => {
            this.refresh()
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      cancel(){
        this.dialogVisible = false
        this.model = {
          train_type: '',
          train_level: '',
          apply_time: '',
          train_num: null,
          train_address:'',
          train_time:''
        }
      }
    },
  };
  </script>
  
  <style lang="scss" scoped></style>