/*** 上传视频弹窗 ***/
.media-upload-video, .media-upload-audio {
  .original-url .el-input__inner {
    padding-right: 60px
  }

  .is-panorama {
    position: absolute;
    right: 8px;
    top: 2px;
    bottom: 2px;
    background: #fff;
  }

  .btn-up {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0
  }

  .el-dialog__body {
    padding-bottom: 10px
  }

  &.submitting {
    touch-action: none;
    pointer-events: none;
  }
}

/*音频上传弹框*/
.media-audio-list {
  .audio-item {
    padding: 10px 8px;
    display: flex;
    align-items: center;
    background-color: #fafafc;

    &:hover {
      color: #3f9eff
    }

    &:nth-child(even) {
      background-color: #fff;
    }

    cursor: pointer;

    .audio-item-check {
      margin-left: 12px;
    }

    .audio-item-title {
      flex: 1;
    }

    .audio-item-icon {
      margin: 0 16px;

      .icon {
        position: relative;
        width: 1em;
        height: 1em
      }

      .icon:hover {
        border: none
      }
    }

    .audio-item-progress {
      position: absolute;
      height: 1px;
      background-color: #67c23a;
      bottom: 0;
    }
  }

}

/*** 搜索视频文件弹窗 ***/
.search-video-dialog {
  .el-dialog {
    top: 50%;
    transform: translate(0, -50%)
  }

  .el-dialog__header, .el-dialog__footer {
    padding: 10px 20px
  }

  .el-dialog__body {
    padding: 20px 20px 0 20px
  }

  .search-key {
    width: 80px;
    border-right: 1px solid #f0f0f0;

    .el-input__inner {
      text-align: center
    }
  }

  .search-val {
    width: 160px
  }

  .search-key input, .search-val input {
    border: none;
    border-radius: 0
  }

  .search-btn {
    margin-left: -30px;
    border: none
  }

  .search-container {
    text-align: center
  }

  .el-pagination {
    display: inline-block;
    float: left
  }
}

.search-video-list {
  height: 480px;
  overflow-y: auto;

  .video-item {
    box-sizing: border-box;
    width: 180px;
    float: left;
    margin: 6px 9px 9px 6px
  }

  .video-cover {
    width: 100%;
    height: auto;
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-color: #F6F8F9;
    padding-bottom: 56.25%;
    display: block;
    margin-bottom: 10px
  }

  .video-title {
    font-size: 14px;
    color: #333
  }

  .video-footer {
    font-size: 12px;
    color: #666;
    display: flex
  }

  .video-created, .video-category {
    flex: 1
  }

  .video-created {
    text-align: right;
    color: #999
  }

  .video-item:nth-child(4n) {
    margin-right: 0
  }

  .video-duration {
    background: rgba(0, 0, 0, 0.5);
    padding: 3px 8px;
    color: #fff;
    font-size: 12px;
    border-radius: 3px;
    position: absolute;
    bottom: 8px;
    right: 8px
  }

  .video-item:hover:after, .video-item.checked:after {
    content: '';
    position: absolute;
    left: -6px;
    top: -6px;
    bottom: -6px;
    right: -6px;
    z-index: 0;
    border: 3px solid #1094fa
  }

  // .video-item.checked:before {
  //   z-index: 1;
  //   content: '';
  //   width: 40px;
  //   height: 40px;
  //   position: absolute;
  //   right: 0;
  //   top: 0;
  //   background: url(cdn('web/img/123.png')) no-repeat
  // }
}

/*** 上传图片弹窗 ***/
.uploadimg-dialog {
  .el-dialog__header {
    display: none
  }

  .el-tabs__header {
    margin-bottom: 20px
  }

  .el-dialog__body {
    padding: 10px 20px 10px 20px
  }

  .el-tab-pane::-webkit-scrollbar {
    display: none
  }

  .img-item {
    float: left;
    display: inline-block;
    list-style: none;
    padding: 0;
    width: 120px;
    height: 120px;
    margin: 0 8px 8px 0;
    background-color: #eee;
    overflow: hidden;
    cursor: pointer;
    position: relative
  }

  .img-item:nth-child(6n) {
    margin-right: 0
  }

  .img-item > .el-image, .img-item > img {
    width: 100%;
    height: 100%
  }

  .upload-content {
    height: 380px;
    overflow: hidden
  }

  .image-meta {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 20px;
    line-height: 20px;
    background: rgba(0, 0, 0, .2);
    text-align: center;
    color: #fff;
    overflow: hidden
  }

  .upimg-tip {
    display: none;
    line-height: 120px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    text-align: center
  }

  .upimg-tip a {
    color: #fff;
    overflow: hidden;
    position: relative
  }

  .upimg-tip a + a {
    margin-left: 10px
  }

  .img-item:hover .upimg-tip {
    display: block
  }

  .icon {
    cursor: pointer;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    border: 0;
    background-repeat: no-repeat;
    box-sizing: border-box
  }

  .icon:hover {
    border: 3px solid #1094fa
  }

  .selected .icon {
    background-image: url('https://cdn.lushan.nextv.show/sys/img-checked.png');
    background-position: top right
  }

  .footer-action {
    text-align: right;
    padding: 10px 0
  }

  .is-upping .upimg-tip, .uploadimg-dialog .is-upping .image-meta, .uploadimg-dialog .is-upping .file {
    display: none !important
  }

  .is-upping .uploading {
    width: 0;
    height: 3px;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 11;
    background: #67c23a
  }

  .is-upping .message {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 12px;
    line-height: 120px;
    text-align: center;
    padding-bottom: 5px
  }

  .el-pagination {
    display: inline-block;
    float: left
  }

  .only-one {
    text-align: center;
  }

  .only-one .img-item {
    margin: 110px auto auto auto;
    float: none;
  }

  .only-one .size-tip {
    display: block
  }

  input[type="file"] {
    cursor: pointer
  }

  .size-tip {
    margin-top: 14px;
    color: #999;
    display: none;
  }
}

/*** 添加资源类型弹窗 ***/
.media-publish-page {
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-type-list {
  display: flex;
  flex-flow: wrap;

  .type-item {
    cursor: pointer;
    width: 120px;
    height: 120px;
    line-height: 120px;
    display: inline-block;
    margin: 15px;
    background-color: #f8f8f8;
    text-align: center;
    border: 2px solid #f8f8f8;

    &.is-disabled {
      color: #b7b7b7;
    }
  }

  .type-item:hover, .type-item.active {
    border: 2px solid #409EFF
  }
}

.media-type-action {
  text-align: center;
  padding: 30px 15px 0 0;
}

.media-table-container, .media-audit-container {
  position: relative;
  display: flex;

  .media-channel-tree {
    flex-shrink: 0;
    position: relative;
    width: 180px;
    padding-right: 30px;
    margin-right: 20px;
    transition: width .3s;

    .split-line {
      z-index: 1;
      cursor: pointer;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      height: 100%;
      width: 6px;
      background: $bg-color;
    }

    &.closed {
      overflow: hidden;
      width: 10px;
      padding: 0;

      .el-tree {
        display: none;
      }
    }

    &::-webkit-scrollbar {
      display: none
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  .audit {
    cursor: pointer;
  }

  .audit-0 {
    color: red;
  }

  .audit-1 {
    color: #67c23a;
  }

  .audit-2 {
    color: #ebb563;
  }

  .cell-title {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      color: #3f8ef7;
    }
  }

  .el-table td + td, .el-table_1_column_2 {
    .cell {
      padding: 0;
    }
  }

  .el-icon-s-check {
    font-size: 16px;
  }
}

.toolbar-header .el-dropdown {
  .el-button:first-child {
    padding: 0;
    border: none;
  }

  input {
    border-right: none;
    width: 110px;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none
  }
}

.media-version-dialog {
  .el-table th {
    padding: 16px 0 12px 0;
  }

  .el-dialog__body {
    padding: 0 0 10px 0;
  }

  .el-table::before {
    display: none;
  }
}

/*** 选择资源弹窗 ***/
.media-select-dialog {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .el-input {
    width: 170px;
  }

  .el-dialog__header {
    padding-top: 15px;
  }

  .el-dialog__title .el-input__inner {
    border: none;
    padding-left: 0;
  }

  .el-dialog__body {
    padding: 10px 20px 10px 20px;
    height: 366px;
    overflow-y: auto;
  }

  .el-dialog__footer {
    display: flex;
    text-align: left;
  }

  .el-pagination {
    flex: 1;
  }

  .el-input__suffix {
    cursor: pointer;
  }

  .none {
    text-align: center;
    margin-top: 160px;
    color: #999;
  }
}

/*** 选择资源列表 ***/
.media-select-table {
  width: 100%;
  height: 100%;

  .tr {
    display: flex;
  }

  .tr:nth-child(2n) {
    background: #fafafc;
  }

  .td {
    padding: 10px 8px;
  }

  .title {
    flex: 1;
    padding: 10px 0;
  }

  .icon {
    color: #cbcbcb;
    cursor: pointer;
  }
}

/*** 频道 ***/
.media-channel-panel {
  .el-cascader-menu {
    flex: 1;
  }
}