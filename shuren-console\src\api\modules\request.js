import service from "@/utils/request";
const request = function (opt) {
  return service(opt);
};

request.get = function (url, params) {
  return service({url: url, method: 'get', params: params});
};

request.post = function (url, data) {
  return service({url: url, method: 'post', data: data});
};

request.head = function (url, data) {
  return service({url: url, method: 'head', params: data});
};
export default request;