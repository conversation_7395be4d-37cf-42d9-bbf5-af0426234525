<template>
  <div class="friendly-link-page">
    <div class="table-container">
      <div class="table-toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{$route.meta.title}}</div>
          <div class="toolbar-actions">
            <el-button size="mini" icon="el-icon-sort" @click="saveSort()" title="保存排序" circle></el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" title="添加友情链接" circle @click="onAdd()"></el-button>
          </div>
        </div>
      </div>
      
      <el-table :data="list" size="mini">
        <el-table-column prop="title" label="链接名称" width="240"></el-table-column>
        <el-table-column prop="visible" label="显示" align="center" width="80">
          <el-tag slot-scope="scope" size="mini" :type="scope.row.visible ? '' : 'danger'">{{scope.row.visible ? '是' : '否'}}</el-tag>
        </el-table-column>
        <el-table-column prop="seq" label="序号" align="center" width="80">
          <el-input size="mini" slot-scope="scope" v-model="scope.row.seq"></el-input>
        </el-table-column>
        <el-table-column prop="url" label="链接地址">
          <a slot-scope="scope" :href="scope.row.url" target="_blank">{{scope.row.url}}</a>
        </el-table-column>
        <el-table-column label="操作" width="90" class-name="table-action">
          <template slot-scope="scope">
            <el-button class="btn-action" size="mini" @click="onDelete(scope.$index)" title="删除">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
              </svg>
            </el-button>
            <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
              </svg>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 编辑 -->
    <el-dialog class="dialog-form" title="友情链接" :visible.sync="dialog" width="600px" :close-on-click-modal="false" :append-to-body="true">
      <el-form ref="form" label-width="80px" :model="model" :rules="rules" size="small">
        <el-row>
          <el-col :span="16">
            <el-form-item label="链接名称" prop="title">
              <el-input v-model="model.title" maxlength="32"></el-input>
            </el-form-item>
            <el-form-item label="链接地址" prop="url">
              <el-input v-model="model.url" maxlength="128"></el-input>
            </el-form-item>
            <el-form-item label="排序序号" prop="seq">
              <el-input v-model.number="model.seq" placeholder="数字越小越靠前"></el-input>
              <el-switch v-model="model.visible"
                         :active-value="1"
                         :inactive-value="0"
                         title="是否显示"
                         style="position:absolute;right:8px;top:7px">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div title="LOGO"
                 :class="{'el-icon-picture-outline-round': !model.icon}"
                 @click="selectImg"
                 :style="`cursor:pointer;margin-left:20px;background:url(${model.icon}) center center / 100% auto no-repeat #f8f8f8;height:135px;width:100%;line-height:135px;text-align:center;font-size:28px;`">
            </div>
          </el-col>
        </el-row>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialog = 0">取 消</el-button>
        <el-button type="primary" @click="submit">保 存</el-button>
      </div>
    </el-dialog>
  
  </div>
</template>

<script>
  import SelectImage from '@/views/media/image/select';
  
  export default {
    name: 'FriendLinkPage',
    data() {
      return {
        list: [],
        dialog: 0,
        model: {
          title: '',
          url: '',
          icon: '',
          seq: 100,
          visible: 1
        },
        rules: {
          title: {required: true, message: '必填项', trigger: 'blur'},
          url: {required: true, message: '必填项', trigger: 'blur'},
          seq: {required: true, message: '范围0~100', trigger: 'blur', type: 'integer', min: 0, max: 100}
        }
      }
    },
    activated() {
      this.refresh();
    },
    methods: {
      refresh() {
        this.dialog = 0;
        this.$api.friendLink.query().then(list => {
          this.list = list;
        });
      },
      saveSort() {
        let list = [];
        this.list.forEach(item => {
          if (/^\d+$/.test(item.seq)) {
            list.push({id: item.id, seq: item.seq});
          }
        });
        
        list.length > 0 && this.$api.friendLink.sort(list).then(this.refresh);
      },
      onDelete(i) {
        this.$confirm('操作不可恢复，确定删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let {list} = this;
          return this.$api.friendLink.delete(list[i].id).then(() => {
            list.splice(i, 1);
          })
        });
      },
      selectImg() {
        SelectImage(1, {compress: 0}).then(list => {
          this.model.icon = list[0].url;
        });
      },
      onAdd() {
        this.model = this.$options.data().model;
        this.dialog = 1;
      },
      onEdit(row) {
        this.model = Object.assign({}, row);
        this.dialog = 1;
      },
      submit() {
        this.$refs.form.validate(valid => {
          if (!valid) return;
          this.$api.friendLink.save(this.model).then(this.refresh)
        });
      }
    }
  }
</script>