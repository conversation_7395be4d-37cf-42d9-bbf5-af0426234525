import Template from './template';

let View;

function show(itemId, reply_id) {
  let el = document.createElement('div');
  document.body.appendChild(el);

  if (!View) {
    View = Vue.extend(Template);
  }

  new View({
    el: el,
    data: {item_id: itemId, reply_id: reply_id},
    methods: {
      onReply(id) {
        if (!reply_id) {
          show(itemId, id);
        }
      }
    }
  });
}

export default {show}