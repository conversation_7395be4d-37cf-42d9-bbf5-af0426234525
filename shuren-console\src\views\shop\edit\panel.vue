<template>
  <div class="edit-shop-page">
    <el-form ref="form" size="small" label-width="100px" :model="model" :rules="rules" style="max-width:1070px">
      <el-row>
        <el-col :span="13">
          <el-form-item label="店铺名称" prop="name">
            <el-input v-model.trim="model.name" maxlength="32" placeholder="请输入" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="营业状态" prop="status">
            <el-select v-model="model.status" placeholder="请选择" style="display:block">
              <el-option label="装修中" :value="0"></el-option>
              <el-option label="营业中" :value="1"></el-option>
              <el-option label="已打样" :value="2"></el-option>
              <el-option label="已注销" :value="3" :disabled="true"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客服电话" prop="hotline">
            <el-input v-model="model.hotline" maxlength="12" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="联系地址" prop="city_id">
            <el-address v-model="address" placeholder="省市区"></el-address>
          </el-form-item>
          <el-form-item label="详细地址" prop="detail" class="set-lgn-lat">
            <el-input v-model="model.address" placeholder="切勿重复省市区，请精确到街道门牌号" suffix-icon="el-icon-location-outline"></el-input>
            <el-button type="primary" size="mini" @click="showMap=true">定位</el-button>
          </el-form-item>
          <el-form-item label="本店简介" prop="intro">
            <el-input class="shop-intro" v-model="model.intro" type="textarea" placeholder="主营业务、售卖品类、企业信息？" maxlength="500" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="Logo" prop="logo">
            <div class="field-logo" :class="{'no-img' : !model.logo}">
              <div class="bg-cover logo-url" :style="`background-image:url(${model.logo})`"></div>
              <div class="logo-tip">128x128<br>.png格式<br>大小不超过300KB</div>
              <div class="bg-cover logo-url is-circle" :style="`background-image:url(${model.logo})`"></div>
              <upload-image v-model="model.logo"></upload-image>
            </div>
          </el-form-item>
          <el-form-item label="主营业务">
            <el-input v-model.trim="model.business_desc" maxlength="50" placeholder="售卖品类"></el-input>
          </el-form-item>
          <el-form-item label="业务负责">
            <el-row>
              <el-col :span="10">
                <el-input v-model.trim="model.leader_name" maxlength="8" placeholder="联系人称呼" title="联系人称呼" suffix-icon="el-icon-user"></el-input>
              </el-col>
              <el-col :span="1">&nbsp;</el-col>
              <el-col :span="13">
                <el-input v-model.trim="model.leader_mobile" maxlength="12" placeholder="联系人电话" title="联系人电话" suffix-icon="el-icon-phone-outline"></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item>
            <el-input v-model.trim="model.leader_wechat" maxlength="32" placeholder="联系人微信" title="联系人微信">
              <svg class="icon" aria-hidden="true" slot="suffix">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#wechat"></use>
              </svg>
            </el-input>
          </el-form-item>
          <el-form-item label="封面图片" prop="images">
            <upload-image class="cover-url" v-model="model.cover_url">600X600</upload-image>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="form-action">
        <el-button size="small" @click="goBack">返 回</el-button>
        <el-button type="primary" size="small" @click="submit">保 存</el-button>
      </el-form-item>
    </el-form>
    <location v-model="location" v-if="showMap" :visible.sync="showMap"></location>
  </div>
</template>

<script>
  import ElAddress from '@/components/address';
  import UploadImage from '@/components/upload/image';
  import Location from '@/components/address/dialog';

  export default {
    components: {ElAddress, Location, UploadImage},
    props: ['value'],
    data() {
      return {
        rules: {
          name: {required: true, message: '请输入', trigger: 'blur'},
          logo: {required: true, message: '请输入', trigger: 'blur'},
        },
        showMap: false
      }
    },
    computed: {
      model() {
        return this.value;
      },
      address: {
        get() {
          let {model} = this;
          return {
            province_id: model.province_id,
            city_id: model.city_id,
            district_id: model.district_id
          }
        },
        set(v) {
          Object.assign(this.model, v);
        }
      },
      location: {
        get() {
          let {model} = this;
          return {
            province_id: model.province_id,
            city_id: model.city_id,
            district_id: model.district_id,
            lng: model.lng,
            lat: model.lat
          }
        },
        set(v) {
          Object.assign(this.model, v);
        }
      }
    },
    methods: {
      goBack() {
        this.$router.go(-1);
      },
      submit() {
        this.$refs.form.validate(valid => {
          if (!valid) return;

          let {model} = this;
          this.$api.shop[model.id ? 'edit' : 'add'](model).then(this.goBack);
        });
      }
    }
  }
</script>

<style lang="scss">
  .edit-shop-page {
    .shop-intro textarea {
      height: 135px;
    }

    .field-logo {
      display: flex;
      align-items: center;
      height: 83px;

      &.no-img {
        border-radius: 4px;
        border: 1px solid #DCDFE6;
      }

      .com-upload-image {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
    }

    .logo-tip {
      flex: 1;
      line-height: 1.4;
      text-align: center;
      color: $color-gray;
    }

    .logo-url {
      width: 83px;
      height: 83px;
      flex-shrink: 0;
      border-radius: 4px;

      &.is-circle {
        border-radius: 50%;
      }
    }

    .cover-url {
      height: 135px;
    }

    .set-lgn-lat {
      .el-button {
        display: none;
        cursor: pointer;
        position: absolute;
        right: 3px;
        top: 3px;
      }

      &:hover .el-button {
        display: block;
      }
    }
  }
</style>