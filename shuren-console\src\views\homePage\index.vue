<template>
  <div>
    <div class="media-table-container">
      <mytable
        ref="table"
        :list="list"
        :api="$api.media.tierList"
        :query="unAuditData"
      >
        <el-form class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <el-input
                class="search-val"
                size="mini"
                v-model="unAuditData.nickname"
                placeholder="请输入"
                style="width: 100px"
                maxlength="5"
                clearable
              >
              </el-input>
              <el-button
                size="mini"
                icon="el-icon-search"
                title="搜索"
                circle
                @click="searchFun"
              ></el-button>
            </div>
          </div>
        </el-form>

        <el-table-column
          prop="id"
          label="编号"
          min-width="4%"
          align="center"
          :formatter="formatter"
        ></el-table-column>
        <el-table-column label="图标" min-width="3%">
          <template slot-scope="scope">
            <div :style="{ textAlign: 'left' }">
              <img
                :src="scope.row.headimg"
                alt=""
                class="logo"
                v-if="scope.row.headimg"
              />
              <p v-else>-----</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          prop="nickname"
          min-width="20%"
        ></el-table-column>

        <el-table-column label="操作" align="center" min-width="10%">
          <template slot-scope="scope">
            <template>
              <el-tag size="small" @click="passFun(scope.row.id)">通过</el-tag>
              <el-tag type="info" size="small" @click="notPassFun(scope.row.id)"
                >不通过</el-tag
              >
            </template>
          </template>
        </el-table-column>
      </mytable>
    </div>
  </div>
</template>

<script>
import MediaBase from "../media/all/base";
import MediaChannel from "../media/channel/components/tree.vue";
import userRequest from "../../utils/userRequest";
export default {
  name: "homePage",
  extends: MediaBase,
  components: { MediaChannel },
  data() {
    return {
      multipleSelection: [],
      name: "",
      list: [],
    };
  },
  computed: {
    list() {
      return [];
    },
  },
  watch: {
    // "query.channel"() {
    //   this.$refs.table.refresh(true);
    // },
    name() {},
  },
  methods: {
    // formatter(row) {
    //   row._type = this.$api.media.type[row.type];
    //   row._status = this.$api.media.status[row.status];
    //   row._pubdate = this.$utils.Date(row.pubdate).format();
    //   return row.id;
    // },
    // refresh() {
    //   this.$refs.table.refresh();
    //   this.needRefresh = 0;
    // },
    onCreated() {},
    passFun(id) {
      userRequest
        .post("/v1/user.account.verify", { user_id: id, audit_status: 1 })
        .then((res) => {
          this.$message({
            message: "审核通过",
            type: "success",
          });
          this.$router.go(0);
        });
    },
    notPassFun(id) {
      userRequest
        .post("/v1/user.account.verify", { user_id: id, audit_status: 2 })
        .then((res) => {
          this.$message.error("审核不通过");
          this.$router.go(0);
        });
    },
  },
};
</script>
<style lang="scss">
.searchBox {
  margin-bottom: 10px;
}
.searchName-val {
  margin-right: 15px;
}
.logo {
  width: 40px;
  height: 40px;
}
</style>
