<template>
  <el-dialog title="修改密码" :visible.sync="visible" width="450px" class="dialog-form"
             :close-on-press-escape="false" :append-to-body="true" @closed="closed">
    <el-form ref="form" label-width="90px" :model="model" :rules="rules" size="small">
      <el-form-item label="当前密码" prop="oldpwd">
        <el-input type="password" v-model="model.oldpwd" placeholder="请输入当前登录密码" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item label="新的密码" prop="password">
        <el-input type="password" v-model="model.password" placeholder="请输入5~32位新密码" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item label="再次输入" prop="password2">
        <el-input type="password" v-model="model.password2" placeholder="请再次输入新密码" maxlength="32"></el-input>
      </el-form-item>
    </el-form>

    <template slot="footer">
      <el-button @click="visible = false" size="small">取 消</el-button>
      <el-button @click="submit" type="primary" size="small" :disabled="loading">修 改</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: true,
      loading: 0,
      model: {
        oldpwd: '',
        password: '',
        password2: ''
      },
      rules: {
        oldpwd: [
          {required: true, message: '必填项', trigger: 'blur'},
          {length: true, min: 5, max: 32, message: '5-32位', trigger: 'blur'}
        ],
        password: [
          {required: true, message: '必填项', trigger: 'blur'},
          {length: true, min: 5, max: 32, message: '5-32位', trigger: 'blur'},
          {
            trigger: 'blur', validator: (rule, value, callback) => {
              callback(this.model.oldpwd == value ? '新旧密码不能一样' : undefined);
            }
          }
        ],
        password2: [
          {required: true, message: '必填项', trigger: 'blur'},
          {
            trigger: 'blur', validator: (rule, value, callback) => {
              let {model} = this;
              callback(model.password != model.password2 ? '两次密码不一致' : undefined);
            }
          }
        ]
      }
    }
  },
  methods: {
    closed() {
      this.$emit('closed');
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        this.loading = 1;

        let model = Object.assign({}, this.model);
        model.oldpwd = this.$utils.md5(model.oldpwd);
        model.password = model.password2 = this.$utils.md5(model.password);

        this.$api.post('/v1/user.account.password', model).then(_ => {
          this.visible = false;
        }).catch(_ => {
          this.loading = 0;
        });
      });
    }
  }
}
</script>