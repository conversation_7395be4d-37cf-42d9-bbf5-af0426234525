<template>
  <div class="staff-page">
    <mytable ref="table" :api="api">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <template v-if="showSort">
              <el-button size="mini" @click="showSort=false">取消</el-button>
              <el-button type="primary" size="mini" @click="saveSort">保存</el-button>
            </template>
            <template v-else>
              <el-button size="mini" @click="showSort=true">排序</el-button>
              <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
            </template>
          </div>
        </div>
      </div>

      <el-table-column label="姓名" prop="name" :formatter="formatter"></el-table-column>
      <el-table-column label="性别" prop="_sex" align="center" width="60px"></el-table-column>
      <el-table-column label="政治面貌" prop="party_name" align="center"></el-table-column>
      <el-table-column label="入党时间" prop="_party_join" align="center"></el-table-column>
      <el-table-column label="出生日期" prop="_birthday" align="center"></el-table-column>
      <el-table-column label="民族" prop="nation_name" align="center" width="80px"></el-table-column>
      <el-table-column label="学历" prop="education_name" align="center"></el-table-column>
      <el-table-column label="显示排序" v-if="showSort" prop="sort" align="center" width="90" class-name="is-sort">
        <input slot-scope="scope" v-model="scope.row.sequence" type="text">
      </el-table-column>
      <el-table-column v-else label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<script>

export default {
  data() {
    return {
      showSort: false
    }
  },
  computed: {
    companyId() {
      return this.$store.state.admin.company_id;
    },
    api() {
      return '/v1/company.staff.query?company_id=' + this.companyId;
    }
  },
  methods: {
    refresh() {
      this.$refs.table.refresh();
    },
    onAdd() {
      this.$router.push({name: 'staff.add', params: {company: this.companyId}})
    },
    onEdit(data) {
      this.$router.push({name: 'staff.edit', params: {company: data.company_id, user: data.id}})
    },
    formatter(row) {
      row._sex = row.sex === 1 ? '男' : row.sex === 2 ? '女' : '不详';
      row._party_join = this.$utils.zhDate(row.party_join);
      row._birthday = this.$utils.zhDate(row.birthday);
      return row.name;
    },
    saveSort() {
      let list = [];

      this.$refs.table.list.forEach(item => {
        if (/^\d+$/.test(item.sequence)) {
          list.push({key: item.id, val: parseInt(item.sequence)});
        }
      });

      this.showSort = false;

      if (list.length > 0) {
        this.$api.post('/v1/company.staff.sort', list).then(this.refresh);
      }
    },
    onDelete(user) {
      this.$confirm('您确定要删除【' + user.name + '】吗?', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.post('/v1/company.staff.delete', {id: user.id}).then(this.refresh);
      })
    }
  }
}
</script>