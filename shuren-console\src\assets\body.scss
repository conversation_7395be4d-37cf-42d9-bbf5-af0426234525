* {
  box-sizing: border-box;
  position: relative;
}

/*** 默认标签样式 ***/
html,body{width:100%;height:100%}

body {
  font: 14px/1.4 Helvetica, STHeiti, Microsoft YaHei, Verdana, Arial, Tahoma, sans-serif;
  color: #414a60;outline: 0;margin: 0;padding: 0;background-color: $bg-color;
}

.c-gray{
  color: $color-gray
}

ul{padding-left:0}
li{list-style:none}
a{color:inherit;text-decoration:none}
a[href],.is-link{color:#409eff;cursor:pointer}

i{font-style: normal;}

input[type=file] {
  z-index: 5;
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  font-size: 120px;
  width: 100%;
  height: 100%;
}

svg.icon {
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -webkit-transition: font-size .25s ease-out 0s;
  transition: font-size .25s ease-out 0s;
  width: 1em;
  height: 1em;
  vertical-align: -.15em;
  fill: currentColor;
  overflow: hidden
}

/***超出文本自动隐藏***/
.ellipsis{white-space:nowrap}
.ellipsis,
.ellipsis-2,
.ellipsis-3{overflow:hidden;text-overflow:ellipsis}
.ellipsis-2,
.ellipsis-3{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}
.ellipsis-2{-webkit-line-clamp:2}
.ellipsis-3{-webkit-line-clamp:3}

.clearfix:after{content:"";display:table;clear:both}

/*** 小箭头 ***/
.arrow-down {
  position: relative;

  &:after{content:"\e6df";font-family:element-icons !important;position:absolute;right:0}
}

/*** 分割线 ***/
.h-line{width:100%;background:$bg-color;padding:5px}
.v-line{height:100%;background:$bg-color;padding:5px}

[class*="-cover"]{
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

/*** 空 ***/

.empty-box{
  padding: 60px 0;
  text-align: center;
}

.empty-img{
  display: block;
  margin: 20px auto;
  width: 160px;
  height: 160px;
}

.empty-label{
  margin-bottom: 20px;
}