import SelectDialog from './select.vue';

let SelectView;

function open(limit, type) {
  return new Promise(function (resolve, reject) {
    if (!SelectView) {
      SelectView = Vue.extend(SelectDialog);
    }

    let list = [];
    let el = document.createElement('div');

    document.body.appendChild(el);

    let view = new SelectView({
      el: el,
      propsData: {
        limit: limit || 1,
        allow: type || 'all'
      }
    });

    view.$on('closed', () => {
      view.$destroy();
      view.$el.remove();

      list.length > 0 ? resolve(list) : reject();
    });

    view.$on('resolve', function (rows) {
      list = rows
    });

    view.visible = true;
  });
}

export default {open}