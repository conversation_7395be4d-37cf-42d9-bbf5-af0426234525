/*** 表格重写 ***/
.el-table {
  .el-input__inner {
    text-align: center;
    border: none;
  }

  .el-tag + .el-tag {
    margin-left: 10px;
  }

  th {
    color: #1e395b !important;
    background-color: #cedff4 !important;
  }

  td.is-sort {
    padding: 0;

    input {
      text-align: center;
      border: none;
      outline: none;
      width: 100%;
      height: 39px;
      line-height: 40px;
      border-bottom: 1px solid #409EFF;
      background: transparent;
    }
  }
}

/*** 弹窗重写 ***/
.dialog-form {
  .el-dialog__body {
    padding-bottom: 10px
  }

  .el-form {
    padding-right: 30px
  }
}

.el-dialog {
  margin: 0 !important;

  &.hide-header .el-dialog__header {
    padding: 0;
  }

  &.abs-close button.el-dialog__headerbtn {
    z-index: 1;
    border-radius: 50%;
    right: -14px;
    top: -14px;
    background: #fff;
    width: 30px;
    height: 30px;
  }
}

.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-dialog__footer {
  .el-button-group {
    margin-left: 10px
  }
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, .3) !important
}


.el-dropdown-menu {
  [class*=" el-icon-"], [class^=el-icon-] {
    position: relative;
    font-size: 16px;
    bottom: -1px;
  }
}

.el-dropdown-menu__item .icon {
  margin-right: 6px;
  font-size: 14px;
}

.el-select-dropdown {
  .load-more {
    text-align: center;
    user-select: none
  }
}

/*** 表单重写 ***/
.el-form {
  .form-action {
    text-align: center;

    &.el-form-item {
      margin-bottom: 0;

      .el-form-item__content {
        background: #fdfdfd;
        padding: 10px 20px;
      }
    }
  }

  .el-select {
    display: block;
  }
}

.el-form-item__content {
  .el-cascader {
    width: 100%;
  }
}

.el-date-editor .el-range-separator {
  width: initial;
}

.el-input__suffix {
  min-width: 25px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.el-tree > .el-tree-node:after {
  border-top: none;
}

.el-tree-node {
  position: relative;
}

.el-tree-node__label {
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-tree-node.is-current > .el-tree-node__content > .el-tree-node__label {
  color: #409eff;
}

.el-tree-node .el-tree-node {
  padding-left: 16px;
}

.el-tree-node__expand-icon.is-leaf:before {
  content: '\e785';
  color: #C0C4CC;
}

.el-tree-node__children {
  padding-left: 16px;
}

.el-tree-node :last-child:before {
  height: 38px;
}

.el-tree > .el-tree-node:before {
  border-left: none;
}

.el-tree > .el-tree-node:after {
  border-top: none;
}

.el-tree-node:before {
  touch-action: none;
  pointer-events: none;
  content: "";
  position: absolute;
  left: -4px;
  top: -26px;
  bottom: 0;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #999;
}

.el-tree-node:after {
  touch-action: none;
  pointer-events: none;
  content: "";
  position: absolute;
  left: -2px;
  top: 12px;
  height: 20px;
  width: 24px;
  border-top: 1px dashed #999;
}

.el-alert__content {
  position: static;
}

.el-radio-group {
  .is-bordered {
    text-align: center;
    margin-left: 0 !important;
    padding: 8px 15px;

    .el-radio__input {
      display: none;
    }

    .el-radio__label {
      padding-left: 0
    }

    &.el-radio--mini {
      padding: 6px 15px;
    }
  }
}

.el-cascader-panel {
  .el-radio {
    z-index: 5;
    opacity: 0;
    position: absolute;
    left: 20px;
    top: 0;
    right: 30px;
    height: 100%;
  }

  .is-disabled + .el-cascader-node__label {
    color: #9c9c9c;
  }
}

.el-slider__marks {
  position: static;
}

.el-slider__marks-text {
  width: fit-content;
}