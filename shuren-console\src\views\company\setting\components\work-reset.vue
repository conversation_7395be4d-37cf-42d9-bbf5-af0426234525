<template>
  <div class="edit-intro">
    <div class="edit-panel-title">作息时间</div>
    <div class="edit-panel-desc">标准时间仅供参考，不代表公司所有职位状况</div>
    <el-form ref="form" :model="model" :rules="rules" class="edit-panel-body" label-width="85px" label-suffix=":" size="small">
      <el-form-item label="工作时间" prop="work_start">
        <el-time-select
            placeholder="起始时间"
            v-model="model.work_start"
            :picker-options="{start: '00:00',step: '00:30', end: '23:30'}">
        </el-time-select>
        <el-time-select
            placeholder="结束时间"
            v-model="model.work_end"
            :picker-options="{start: '00:30',step: '00:30',end: '24:00', minTime: model.work_start}"
            style="margin-left:30px">
        </el-time-select>
      </el-form-item>
      <el-form-item label="加班情况" prop="overtime">
        <el-dict-radio v-model="model.overtime" type="workOvertime"></el-dict-radio>
      </el-form-item>
      <el-form-item label="休息时间" prop="rest_code">
        <el-dict-radio v-model="model.rest_code" type="restTime"></el-dict-radio>
      </el-form-item>
    </el-form>
    <div class="edit-panel-action">
      <el-button type="primary" size="small" @click="onSubmit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import ElDictRadio from '@/components/dictionary/radio';

export default {
  components: {ElDictRadio},
  data() {
    return {
      rules: {
        work_start: {
          required: true, trigger: 'blur', validator: (rule, value, callback) => {
            callback(!this.model.work_start || !this.model.work_end ? new Error('请选择') : undefined);
          }
        }
      }
    }
  },
  computed: {
    model() {
      return this.$parent.model;
    }
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        valid && this.doSubmit();
      });
    },
    doSubmit() {
      let {model} = this;

      this.$api.post('/v1/company.info.update?action=workReset', {
        id: model.id,
        work_start: model.work_start,
        work_end: model.work_end,
        overtime: model.overtime,
        rest_code: model.rest_code
      });
    }
  }
}
</script>