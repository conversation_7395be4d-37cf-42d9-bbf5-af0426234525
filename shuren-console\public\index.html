<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, maximum-scale=1, minimum-scale=1" />
    <title>控制台</title>
    <meta name="renderer" content="webkit" />
    <meta name="referrer" content="no-referrer" />
    <link rel="shortcut icon" href="<%= process.env.VUE_APP_CDN_URL %>/sys/favicon.ico" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <meta name="wap-font-scale" content="no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telphone=no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <link rel="stylesheet" href="https://cdn.nextv.show/npm/element-ui@2.13.2/lib/theme-chalk/index.css" />
  </head>

  <body>
    <div id="app">
      <script src="https://cdn.nextv.show/npm/crypto-js@3.1.9-1/crypto-js.min.js"></script>
      <script src="https://cdn.nextv.show/npm/vue@2.6.11/dist/vue.min.js"></script>
      <script src="https://cdn.nextv.show/npm/vue-router@3.1.6/dist/vue-router.js"></script>
      <script src="https://cdn.nextv.show/npm/vuex@3.1.2/dist/vuex.min.js"></script>
      <script src="https://cdn.nextv.show/npm/element-ui@2.13.2/lib/index.js"></script>
      <script src="https://cdn.nextv.show/npm/axios@0.19.0/dist/axios.min.js"></script>
      <script src="<%= process.env.VUE_APP_CDN %>/js/iconfont.js?v=6" async="async"></script>
      <script src="https://cdn.nextv.show/npm/validator@12.1.0/validator.min.js" async="async"></script>
       <!-- 获取地理位置 -->
      <!-- <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=43e0bc7d5e563a10819cd13deb9f8eb7&callback=onLoad"></script> -->
      <script type="text/javascript">
          // 一定要安全密钥先写在前，key在后，不然无效
          window._AMapSecurityConfig = {
              securityJsCode: "67d0125a5fab67f7a67b2b1a51bf47a7",
          }
      </script>
    </div>
  </body>
</html>
