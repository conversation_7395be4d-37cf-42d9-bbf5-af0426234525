<template>
  <div class="cropper-container">
    <div class="cropper-view"
         :style="style"
         @mousedown.stop.prevent="onStartDrag"
         @touchstart.stop.prevent="onStartDrag"
    >
      <div v-for="handle in directions"
           :key="handle"
           :class="`handle handle-${handle}`"
           @mousedown.stop.prevent="onStartMove($event, handle)"
           @touchstart.stop.prevent="onStartMove($event, handle)"
      >
      </div>
      <svg aria-hidden="true" class="icon drag">
        <use xlink:href="#pano-fullscreen"></use>
      </svg>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    props: {
      minWidth: {
        default: 100
      },
      minHeight: {
        default: 100
      },
      maxWidth: {
        default: null
      },
      maxHeight: {
        default: null
      },
      input: {
        default() {
          return {x: 0, y: 0, w: 0, h: 0};
        }
      }
    },
    data: function () {
      return {
        directions: ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
        style: '',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      }
    },
    methods: {
      offset() {
        let {x, y} = this.$el.getBoundingClientRect();
        return [x, y];
      },
      reset() {
        let width = this.$el.clientWidth;
        let height = this.$el.clientHeight;

        this.style = {
          left: (this.left / width * 100) + '%',
          top: (this.top / height * 100) + '%',
          right: (this.right / width * 100) + '%',
          bottom: (this.bottom / height * 100) + '%'
        };

        this.$emit('input', {
          x: this.left,
          y: this.top,
          w: width - this.left - this.right,
          h: height - this.top - this.bottom
        });
      },
      onStartMove(e, p) {
        this.resetLimit(e, p);
        window.addEventListener('mousemove', this.onMoving, true);
        window.addEventListener('touchmove', this.onMoving, true);
        window.addEventListener('mouseup', this.onMoveEnd, true);
        window.addEventListener('touchend', this.onMoveEnd, true);
      },
      onMoving(e) {
        this.setPosition(
          e.type == 'mousemove' ? e.clientX : e.touches[0].clientX,
          e.type == 'mousemove' ? e.clientY : e.touches[0].clientY
        );
      },
      onMoveEnd(e) {
        window.removeEventListener('mouseup', this.onMoveEnd, true);
        window.removeEventListener('touchend', this.onMoveEnd, true);
        window.removeEventListener('mousemove', this.onMoving, true);
        window.removeEventListener('touchmove', this.onMoving, true);
      },
      resetLimit(e, p) {
        let elv = this.$el.querySelector('.cropper-view'),
          width = this.$el.clientWidth,
          height = this.$el.clientHeight,
          minWidth = Math.min(this.minWidth, width),
          minHeight = Math.min(this.minHeight, height),
          maxWidth = this.maxWidth ? Math.min(this.maxWidth, width) : width,
          maxHeight = this.maxHeight ? Math.min(this.maxHeight, height) : height;

        let [offsetX, offsetY] = this.offset();

        return this.limit = {
          adjust: this.getAdjust(p),
          width: width,
          height: height,
          minWidth: minWidth,
          minHeight: minHeight,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          offsetX: offsetX,
          offsetY: offsetY,
          minLeft: Math.max(width - this.right - maxWidth, 0),
          maxLeft: Math.max(width - this.right - minWidth, 0),
          minRight: Math.max(width - this.left - maxWidth, 0),
          maxRight: Math.max(width - this.left - minWidth, 0),
          minTop: Math.max(height - this.bottom - maxHeight, 0),
          maxTop: Math.max(height - this.bottom - minHeight, 0),
          minBottom: Math.max(height - this.top - maxHeight, 0),
          maxBottom: Math.max(height - this.top - minHeight, 0),
          cw: elv.clientWidth,
          ch: elv.clientHeight
        };
      },
      getAdjust(p) {
        switch (p) {
          case 'tl':
            return ['top', 'left'];
          case 'tm':
            return ['top'];
          case 'tr':
            return ['top', 'right'];
          case 'mr':
            return ['right'];
          case 'br':
            return ['bottom', 'right'];
          case 'bm':
            return ['bottom'];
          case 'bl':
            return ['bottom', 'left'];
          case 'ml':
            return ['left'];
        }
      },
      setPosition(x, y) {
        let {limit} = this;

        x -= limit.offsetX;
        y -= limit.offsetY;

        limit.adjust.forEach(p => {
          switch (p) {
            case 'left':
              if (x < limit.minLeft) {
                x = limit.minLeft;
              } else if (x > limit.maxLeft) {
                x = limit.maxLeft;
              }
              this.left = x;
              break;
            case 'right':
              x = limit.width - x;
              if (x < limit.minRight) {
                x = limit.minRight;
              } else if (x > limit.maxRight) {
                x = limit.maxRight;
              }
              this.right = x;
              break;
            case 'top':
              if (y < limit.minTop) {
                y = limit.minTop;
              } else if (y > limit.maxTop) {
                y = limit.maxTop;
              }
              this.top = y;
              break;
            case 'bottom':
              y = limit.height - y;
              if (y < limit.minBottom) {
                y = limit.minBottom;
              } else if (y > limit.maxBottom) {
                y = limit.maxBottom;
              }
              this.bottom = y;
              break;
          }
        });

        this.reset();
      },
      onStartDrag(e) {
        let elv = this.$el.querySelector('.cropper-view'),
          width = this.$el.clientWidth,
          height = this.$el.clientHeight,
          minWidth = Math.min(this.minWidth, width),
          minHeight = Math.min(this.minHeight, height),
          maxWidth = this.maxWidth ? Math.min(this.maxWidth, width) : width,
          maxHeight = this.maxHeight ? Math.min(this.maxHeight, height) : height;

        let [offsetX, offsetY] = this.offset();

        this.limit = {
          width: width,
          height: height,
          minWidth: minWidth,
          minHeight: minHeight,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          offsetX: offsetX,
          offsetY: offsetY,
          minLeft: 0,
          maxLeft: width - elv.clientWidth,
          minRight: 0,
          maxRight: width - elv.clientWidth,
          minTop: 0,
          maxTop: height - elv.clientHeight,
          minBottom: 0,
          maxBottom: height - elv.clientHeight,
          cw: elv.clientWidth,
          ch: elv.clientHeight
        };

        window.addEventListener('mousemove', this.onDraging, true);
        window.addEventListener('touchmove', this.onDraging, true);
        window.addEventListener('mouseup', this.onDragEnd, true);
        window.addEventListener('touchend', this.onDragEnd, true);
      },
      onDraging(e) {
        let {limit} = this;
        let x = (e.type == 'mousemove' ? e.clientX : e.touches[0].clientX) - limit.offsetX;
        let y = (e.type == 'mousemove' ? e.clientY : e.touches[0].clientY) - limit.offsetY;

        let left = x - limit.cw / 2;
        if (left < limit.minLeft) {
          left = limit.minLeft;
        } else if (left > limit.maxLeft) {
          left = limit.maxLeft;
        }

        let top = y - limit.ch / 2;
        if (top < limit.minTop) {
          top = limit.minTop;
        } else if (top > limit.maxTop) {
          top = limit.maxTop;
        }

        this.left = left;
        this.top = top;
        this.right = limit.width - left - limit.cw;
        this.bottom = limit.height - top - limit.ch;

        this.reset();
      },
      onDragEnd(e) {
        window.removeEventListener('mousemove', this.onDraging, true);
        window.removeEventListener('touchmove', this.onDraging, true);
        window.removeEventListener('mouseup', this.onDragEnd, true);
        window.removeEventListener('touchend', this.onDragEnd, true);
      },
      onResize() {
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {

          let el = this.$el.querySelector('.cropper-view');
          let style = window.getComputedStyle(el, null);

          this.$emit('input', {
            x: parseFloat(style.getPropertyValue('left')),
            y: parseFloat(style.getPropertyValue('top')),
            w: el.clientWidth,
            h: el.clientHeight
          });

        }, 600);
      }
    },
    mounted() {
      this.reset();
      window.addEventListener('resize', this.onResize, true);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.onResize, true);
    }
  }
</script>

<style lang="scss" src="./style.scss"></style>