<template>
  <div class="layout-container">
    <layout-sidebar></layout-sidebar>
    <div class="layout-body">
      <layout-header></layout-header>
      <div class="layout-content" id="layoutContent">
        <keep-alive :include="/Page$/">
          <router-view class="layout-view"/>
        </keep-alive>
        <layout-footer></layout-footer>
        <el-backtop target=".layout-content"></el-backtop>
      </div>
    </div>
  </div>
</template>

<script>
import LayoutFooter from './footer';
import LayoutSidebar from './sidebar';
import LayoutHeader from './header';

export default {
  components: {LayoutFooter, LayoutHeader, LayoutSidebar},
  beforeCreate() {
    this.$store.dispatch('nav/sidebar');

    this.$store.dispatch('admin/checkLogin').then(res => {
      if (res.status == -1) {
        location.replace('/login');
      }
    });
  }
}
</script>

<style lang="scss" src="./style.scss"></style>