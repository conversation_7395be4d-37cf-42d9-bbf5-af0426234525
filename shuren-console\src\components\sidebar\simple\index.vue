<template>
  <div class="sidebar-simple">
    <div class="menu-list">
      <sidebar-item
        v-for="(menu, index) in list"
        :key="index"
        :menu="menu"
        :level="0"
        :active="active"
      ></sidebar-item>
    </div>
  </div>
</template>

<script>
import SidebarItem from "./item";

export default {
  name: "sidebar-simple",
  components: { SidebarItem },
  props: ["list", "active"],
};
</script>