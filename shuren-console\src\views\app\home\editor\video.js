import Base from './base'

export default {
  name: 'EditorVideo',
  extends: Base,
  render(h) {
    let style = this.getStyle()
    // if (!this.$store.state.admin.isSpecial) {
    return (
      <div class="editor-com editor-video" style={style} ondblclick={this.onBgImage}>
        <div class="editor-select-link" title="点击更改链接地址" onClick={this.onLink}>
          <i class="el-icon-link"></i>
        </div>
        <div>{this.$slots.default}</div>
      </div>
    )
    // } else {
    //   return (
    //     <div class="editor-com editor-video" style={style}>
    //       <div class="play-button">
    //         <svg aria-hidden="true" class="icon">
    //           <use xlinkHref="#play-border"></use>
    //         </svg>
    //       </div>
    //       <div class="editor-select-link" title="点击更改链接地址" onClick={this.onVideo}>
    //         <i class="el-icon-link"></i>
    //       </div>
    //       {this.$slots.default}
    //     </div>
    //   )
    // }
  },
}
