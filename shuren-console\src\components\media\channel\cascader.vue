<template>
  <el-cascader class="media-channel-cascader" v-model="value" :size="size" :placeholder="placeholder" :options="list"
               :props="{ expandTrigger: 'hover', label: 'name', value: 'id' }" @change="onChange"></el-cascader>
</template>

<script>
export default {
  name: "ElChannel",
  props: {
    value: {},
    size: {
      default: "small",
    },
    placeholder: {
      default: "请选择",
    },
  },
  data() {
    return {
      list: [],
    };
  },
  created() {
    this.reqList()
  },
  methods: {
    onChange(arr) {
      let v = arr[arr.length - 1] || null;
      this.$emit("input", v);
      this.$emit("change", v);
    },
    async reqList() {
      const list = await this.$api.media.channel
        .list({ owner: this.$store.state.admin.id })
      const a = this.getTreeData(list)
      this.list = a

    },
    getTreeData(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // 最后一级没有数据将children变成undefined
          data[i].children = undefined;
        } else {
          // children不为空时继续调用该方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
  },
};
</script>