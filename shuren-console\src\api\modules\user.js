import request from './request';

export default {
  account: {
    captcha(type, number) {
      return request.post('/v1/user.account.captcha', {type: type, number: number});
    },
    register(model) {
      return request.post('/v1/user.account.register', model);
    },
    query(query) {
      return request.get('user.account.query', query);
    },
    password(data) {
      return request.post('user.account.password', data);
    },
    unbind(id, type, value) {
      return request.post('user.account.unbind', {id: id, type: type, value: value});
    },
    get(id) {
      return request.get('user.account.get?id=' + id);
    },
    create(data) {
      return request.post('user.account.create', data);
    },
    update(data) {
      return request.post('user.account.update', data);
    },
    exists(username) {
      return request.get('user.account.exists?username=' + encodeURIComponent(username));
    }
  },
  realname: {
    get(uid) {
      return request.get('user.realname.get?uid=' + uid);
    },
    save(model, result) {
      return request.post('user.realname.save', Object.assign({}, model, {result: result}));
    }
  },
  wallet: {
    account(uid) {
      return request.get('user.wallet.account?uid=' + uid);
    },
    activate(no) {
      return request.post('user.wallet.activate', {number: no});
    },
    frozen(no) {
      return request.post('user.wallet.frozen', {number: no});
    },
    adjust(no, data) {
      return request.post('user.wallet.adjust', Object.assign({number: no}, data));
    },
    flow(q) {
      return request.get('user.wallet.flow', q);
    }
  },
  role: {
    all(uid) {
      return request.get('user.role.all?user_id=' + uid);
    },
    save(uid, roles) {
      return request.post('user.role.save', {user_id: uid, roles: roles});
    }
  },
  getEdit(id) {
    return request.get('user.info.getEdit?id=' + id)
  }
};
