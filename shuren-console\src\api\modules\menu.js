import request from './request';

export default {
  sidebar() {
    return request.get('/v1/sys.access.menu');
  },
  query(query) {
    return request.get('/v1/sys.menu.query', query).then(list => {
      let split = '', i;

      list.forEach(item => {
        if (item.level > 1) {
          split = '├';
          for (i = item.level - 1; i > 0; i--) split += '─';
        } else {
          split = '';
        }

        item.title2 = split + item.title;
      });

      return list;
    });
  },
  create(data) {
    return request.post('/v1/sys.menu.create', data);
  },
  update(data) {
    return request.post('/v1/sys.menu.update', data);
  },
  delete(id) {
    return request.post('/v1/sys.menu.delete', {id: id});
  }
};