<template>
  <el-dialog title="经纬度定位" width="600px" :visible.sync="visible"
             class="address-map-dialog"
             :append-to-body="true"
             :close-on-click-modal="false"
             :close-on-press-escape="false"
             @closed="closed">
    <map-location :model="model" style="width:100%;height:350px;"></map-location>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible=false" size="small">取 消</el-button>
      <el-button type="primary" @click="submit" size="small">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import MapLocation from './map';

  export default {
    components: {MapLocation},
    props: ['value', 'visible'],
    data() {
      return {
        model: Object.assign({
          lng: 0,
          lat: 0,
          province_id: 0,
          city_id: 0,
          district_id: 0,
          province_name: '',
          city_name: '',
          district_name: '',
          address: ''
        }, this.value)
      }
    },
    methods: {
      closed() {
        this.$emit('update:visible', false);
      },
      submit() {
        let value = Object.assign(this.value, this.model);
        this.$emit('input', value);
        this.$emit('changed', value);
        this.closed();
      }
    }
  }
</script>