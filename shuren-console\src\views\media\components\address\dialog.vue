<template>
  <el-dialog class="video-modal" :title="`播放地址·${model.text}`"
             :visible.sync="visible" @closed="closed" :append-to-body="true" width="600px">
    <el-form ref="form" label-width="100px" auto-complete="off" :model="model" :rules="rules" style="margin-right:40px;" size="small">
      <el-form-item label="分辨率" prop="resolution">
        <el-select v-model="model.resolution" :disabled="disabled" filterable allow-create placeholder="请选择或输入" style="display:block">
          <el-option label="960 x 540" value="960x540"></el-option>
          <el-option label="1280 x 720" value="1280x720"></el-option>
          <el-option label="1920 x 1080" value="1920x1080"></el-option>
          <el-option label="3840 x 2160" value="3840x2160"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="别名" prop="text">
        <el-select v-model="model.text" :disabled="disabled" style="display:block">
          <el-option value="流畅"></el-option>
          <el-option value="标清"></el-option>
          <el-option value="高清"></el-option>
          <el-option value="超清"></el-option>
          <el-option value="4K"></el-option>
          <el-option value="原画"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="mp4" prop="mp4">
        <el-input v-model="model.mp4" :placeholder="placeholder" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="m3u8" prop="m3u8">
        <el-input v-model="model.m3u8" :placeholder="placeholder" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="flv" prop="flv">
        <el-input v-model="model.flv" :placeholder="placeholder" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="rtmp" prop="rtmp">
        <el-input v-model="model.rtmp" placeholder="必须以rtmp://开头" :disabled="disabled"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <template v-if="disabled">
        <el-button type="primary" size="small" @click="visible=false">关 闭</el-button>
      </template>
      <template v-else>
        <el-button @click="remove" size="small">移 除</el-button>
        <el-button type="primary" size="small" @click="submit">确 定</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    props: ['value', 'visible', 'disabled'],
    data() {
      let scope = this;

      return {
        model: Object.assign({}, this.value),
        rules: {
          resolution: {required: true, trigger: 'blur', message: '请选择或输入'},
          mp4: {trigger: 'blur', validator: scope.validUrl},
          flv: {trigger: 'blur', validator: scope.validUrl},
          m3u8: {trigger: 'blur', validator: scope.validUrl},
          rtmp: {trigger: 'blur', validator: scope.validRtmp}
        }
      }
    },
    computed: {
      placeholder() {
        return '必须以' + (location.protocol == 'http:' ? 'http://或https://' : 'https://') + '开头'
      }
    },
    watch: {
      value(v) {
        this.model = Object.assign({}, v);
      }
    },
    methods: {
      closed() {
        this.$refs.form.clearValidate();
        this.$emit('update:visible', false);
        this.$emit('closed');
        this.$el.remove();
      },
      hasUrl() {
        let {model} = this;
        return model.mp4 || model.flv || model.m3u8 || model.rtmp;
      },
      validUrl(rule, value, callback) {
        if (!this.hasUrl()) {
          callback('请至少输入一个可播放地址');
        } else if (!value || validator.isURL(value, {protocols: ['http', 'https']})) {
          callback()
        } else {
          callback('格式错误')
        }
      },
      validRtmp(rule, value, callback) {
        if (!this.hasUrl()) {
          callback('请至少输入一个可播放地址');
        } else if (!value || validator.isURL(value, {protocols: ['rtmp']})) {
          callback()
        } else {
          callback('格式错误')
        }
      },
      submit() {
        this.$refs.form.validate(valid => {
          if (!valid) return;

          let {model} = this;
          model.checked = true;

          Object.assign(this.value, model);
          this.visible = false;
        });
      },
      remove() {
        this.value.checked = false;
        this.visible = false;
      }
    }
  }
</script>