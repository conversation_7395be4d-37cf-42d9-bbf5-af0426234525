import request from './request';
import utils from '@/utils';

export default {
  queryReply(query) {
    return request.get('discuss.queryReply', query).then(res => {
      res.rows.forEach(item => {
        item.created = utils.Date(item.created).format();
        item.content = utils.emoji.decode(item.content);

        if(item.at){
          item.at.content = utils.emoji.decode(item.at.content);
        }
      });

      return res;
    });
  },
  enabled(id, val){
    return request.post('discuss.enabled', {id: id, enabled: val});
  }
};