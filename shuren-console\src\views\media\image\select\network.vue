<template>
  <div class="upload-body">
    <div class="upload-content">
      <el-input
          v-model="url"
          placeholder="请输入https图片地址"
          maxlength="128"
          size="mini"
          @blur="doLoad"
          @keypress="doLoad"
          clearable>
      </el-input>

      <div style="height:320px;margin-top:20px;position:relative">
        <div :style="bgStyle">
          <img :src="imgUrl" ref="img" alt style="display:none" @load="onLoad" @error="onError">
        </div>
      </div>
    </div>

    <div class="footer-action">
      <slot name="cancel"></slot>
      <el-button type="primary" @click="useImg" :disabled="!loaded">使用</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      url: null,
      imgUrl: null,
      width: 0,
      height: 0,
      loaded: false
    }
  },
  computed: {
    bgStyle() {
      let {width, height, imgUrl} = this, style = 'position:absolute;left:0;top:0;width:100%;height:100%;border-radius:4px;overflow:hidden;';

      if (!imgUrl || width == 0) return style + 'border:1px solid #DCDFE6;';

      style += 'background-repeat:no-repeat;background-position: 50% 50%;background-image:url(' + imgUrl + ');';

      if (width == height || (width <= 700 && height <= 320)) {
        style += 'background-size: auto;';
      } else if (width > height * 2) {
        style += 'background-size:100% auto;';
      } else if (height > width * 2) {
        style += 'background-size:auto 100%;';
      } else {
        style += 'background-size: cover;';
      }

      return style;
    }
  },
  methods: {
    onLoad() {
      this.loaded = true;
      let img = this.$refs.img;
      this.width = img.naturalWidth;
      this.height = img.naturalHeight;
    },
    onError() {
      if (this.imgUrl) {
        this.loaded = false;
        this.width = 0;
        this.height = 0;
        this.$notify.error({message: '图片加载失败'});
      }
    },
    doLoad(e) {
      if (e.type != 'blur' && (e.type == 'keypress' && e.key != 'Enter')) return;
      this.loaded = false;
      this.imgUrl = this.url;
    },
    useImg() {
      this.$emit('ok', [{
        width: this.width,
        height: this.height,
        url: this.imgUrl
      }]);
    }
  }
}
</script>