<template>
  <div class="layout-sidebar">
    <avatar></avatar>
    <sidebar :list="list" :active="active" @select="onSelect"></sidebar>
  </div>
</template>

<script>
import avatar from "./avatar";
import sidebar from "@/components/sidebar";

export default {
  components: { sidebar, avatar },
  computed: {
    list() {
      return this.$store.state.nav.sidebar;
    },
    active() {
      return this.$store.state.nav.active;
    },
  },
  watch: {
    $route(to, from) {
      this.addTag(to, from);
    },
  },
  mounted() {
    this.addTag(this.$route);
  },
  methods: {
    onSelect(menu) {
      if (this.$route.fullPath != menu.path) {
        this.$router.push(menu.path);
      }
    },
    addTag(route, from) {
      if (from && from.name != route.name) {
        let $com = from.matched[from.matched.length - 1];
        if (!/^page-/.test($com.components.default.name)) {
          this.$store.dispatch("nav/close", from);
        }
      }

      this.$store.dispatch("nav/active", route);
    },
  },
};
</script>