<template>
  <div class="edit-base">
    <div class="edit-panel-title">基本信息</div>
    <div class="edit-panel-desc">组织基本信息维护</div>
    <el-form ref="form" class="edit-panel-body" :model="model" :rules="rules" label-width="90px" label-suffix=":"
             size="small">
      <el-form-item label="全称" prop="name">
        <el-input v-model.trim="model.name" placeholder="请输入" maxlength="64"></el-input>
      </el-form-item>
      <el-form-item label="简称" prop="alias">
        <el-input v-model.trim="model.alias" placeholder="请输入" maxlength="16"></el-input>
      </el-form-item>
      <el-form-item label="照片" prop="company_image">
        <cover-image v-model="model.company_image" style="width:400px;height:233px;" empty-text="支部合照"></cover-image>
      </el-form-item>
      <el-form-item label="背景风格" prop="company_background">
        <el-radio-group v-model="model.company_background" size="small">
          <el-radio :label="0" border>蓝色风格</el-radio>
          <el-radio :label="1" border>红色风格</el-radio>
        </el-radio-group>
      </el-form-item>
      <!--屏蔽党建无关的信息
      <el-form-item label="所在行业">
        <select-industry v-model="model.industry_code" type="industry"></select-industry>
      </el-form-item>
      <el-form-item label="融资阶段">
        <dictionary v-model="model.stage_code" type="financingStage"></dictionary>
      </el-form-item>
      <el-form-item label="人员规模">
        <dictionary v-model="model.scale_code" type="staffScale"></dictionary>
      </el-form-item>
      -->
    </el-form>
    <div class="edit-panel-action">
      <el-button type="primary" @click="onSubmit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import CoverImage from "@/views/media/image/cover";
import Dictionary from "@/components/dictionary/select";
import SelectIndustry from "@/components/dictionary/industry/select";

export default {
  components: { Dictionary, SelectIndustry, CoverImage },
  data() {
    return {
      rules: {
        name: { required: true, trigger: "blur", message: "必填项" },
        alias: { required: true, trigger: "blur", message: "必填项" },
        company_image: { required: true, trigger: "blur", message: "必填项" },
      },
    };
  },
  computed: {
    model() {
      return this.$parent.model;
    },
  },
  methods: {
    upLogo() {
      UploadImage(1).then((list) => {
        this.model.logo = list[0].url;
      });
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        valid && this.doSubmit();
      });
    },
    doSubmit() {
      let { model } = this;
      this.$api.post("/v1/company.info.update?action=base", {
        id: model.id,
        logo: model.logo,
        name: model.name,
        alias: model.alias,
        industry_code: model.industry_code,
        stage_code: model.stage_code,
        scale_code: model.scale_code,
        company_background: model.company_background,
        company_image: model.company_image
      });
    },
  },
};
</script>