<template>
  <div>
    <div class="edit-panel-title">更新数据</div>

    <div class="edit-panel-body">
      <el-empty v-if="message.length==0" label="暂无消息"></el-empty>
      <div v-else style="max-height: 40vh;overflow-y: auto;background: #f0f0f0;padding: 20px;margin-bottom:20px">
        <div v-for="str in message">{{ str }}</div>
      </div>
      <div v-if="status==7">{{ download.url }}</div>

      <el-progress v-if="status==7" :text-inside="true" :stroke-width="24" :percentage="download.progress" status="success"></el-progress>
    </div>

    <div class="edit-panel-action">
      <el-button type="primary" v-if="status==1" disabled>开始更新</el-button>
      <el-button type="primary" v-else-if="status==2" @click="retryCheck">重新检查</el-button>
      <el-button type="primary" v-else-if="status==3" @click="hideDialog">完成</el-button>
      <template v-else-if="status==4">
        <el-button @click="cancelUpdate">取消</el-button>
        <el-button type="primary" @click="startUpdate">立即更新({{ countdown }}秒)</el-button>
      </template>
      <el-button type="primary" v-else-if="status==5" disabled>数据处理中</el-button>
      <el-button type="primary" v-else-if="status==6" @click="cancelUpdate">更新失败</el-button>
      <el-button type="primary" v-else-if="status==7" disabled>{{ download.button }}</el-button>
      <template v-else-if="status==8">
        <el-button type="primary" @click="forceOk">强制完成</el-button>
        <el-button type="primary" @click="retryDownload">重试下载({{ countdown }}秒)</el-button>
      </template>
      <el-button type="primary" v-else-if="status==9">更新成功</el-button>
      <el-button type="primary" v-else @click="notifyAppUpdateData">推送更新</el-button>
    </div>
  </div>
</template>

<script>
import ElEmpty from "@/components/empty/view";

export default {
  components: {ElEmpty},
  data() {
    return {
      status: 0,
      loading: 0,
      message: [],
      download: null,
      countdown: 60
    }
  },
  computed: {
    socket() {
      return this.$parent.socket;
    }
  },
  mounted() {
    this.socket.on('app update state', this.onAppUpdateState);
  },
  methods: {
    notifyAppUpdateData() {
      this.socket.emit('notify app update data');
    },
    onAppUpdateState(data) {
      if (data.status == 1) {
        this.message.length = 0;
      }

      if (data.message) {
        if (Array.isArray(data.message)) {
          this.message.push(...data.message);
        } else {
          this.message.push(data.message);
        }
      }

      this.download = data.download;
      this.countdown = data.countdown;
      this.loading = data.loading;
      this.status = data.status;
    },
    retryCheck() {
      this.socket.emit('notify app check update');
    },
    startUpdate() {
      this.socket.emit('notify app update now');
    },
    retryDownload() {
      this.socket.emit('notify app update retry download');
    },
    rebootApp() {
      this.socket.emit('notify app reboot');
    },
    forceOk() {
      this.socket.emit('notify app update force success');
    },
    hideDialog() {
      this.socket.emit('notify app hide update');
    },
    cancelUpdate() {
      this.socket.emit('notify app update cancel');
    }
  }
}
</script>