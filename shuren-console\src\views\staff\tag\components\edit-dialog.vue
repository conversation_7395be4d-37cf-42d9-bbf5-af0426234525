<template>
  <el-dialog
      :title="model.id ? '编辑分组': '添加分组'"
      :visible.sync="dialogVisible"
      width="500px"
      class="edit-staff-group-dialog"
      @closed="onClosed"
      :append-to-body="true">

    <el-form ref="form" label-width="80px" :model="model" :rules="rules">
      <el-form-item label="分组名称" prop="name">
        <el-input v-model.trim="model.name" maxlength="15" placeholder="必填项"></el-input>
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model.trim="model.sort" maxlength="3" placeholder="0-999，数字越大越靠前"></el-input>
      </el-form-item>
      <el-form-item style="text-align: right">
        <el-checkbox v-model="model.hidden" :true-label="0" :false-label="1">显示到组织架构中</el-checkbox>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="onSubmit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: ['visible', 'editing'],
  data() {
    return {
      dialogVisible: false,
      model: {
        id: null,
        name: '',
        company_id: this.$store.state.admin.company_id,
        sort: '',
        hidden: 0
      },
      rules: {
        name: {required: true, message: '必填项'},
        sort: {message: '0-999之间的整数', pattern: /^\d+$/}
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        this.dialogVisible = v;
      }
    },
    editing(v) {
      let form = this.$refs.form;
      form && form.resetFields();
      Object.assign(this.model, v);
    }
  },
  methods: {
    onClosed() {
      this.$emit('update:visible', false)
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        this.dialogVisible = false;

        let {model} = this;
        this.$api.post('/v1/company.staff.tag.' + (model.id ? 'update' : 'create'), model).then(_ => {
          this.$emit('saved');
        });
      });
    }
  }
}
</script>