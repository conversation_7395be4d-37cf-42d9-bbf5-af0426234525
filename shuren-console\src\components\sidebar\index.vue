<template>
  <component
    class="sidebar-container"
    v-bind:is="style"
    :list="list"
    :active="active"
  ></component>
</template>

<script>
import SidebarSimple from "./simple";

export default {
  name: "sidebar",
  components: { SidebarSimple },
  props: {
    type: {
      default() {
        return "simple";
      },
    },
    list: {
      default() {
        return [];
      },
    },
    active: {
      default() {
        return 0;
      },
    },
  },
  computed: {
    style() {
      return "sidebar-" + this.type;
    },
  },
  methods: {
    onClick(menu) {
      this.$emit("select", menu);
    },
  },
};
</script>

<style lang="scss" src="./style.scss"></style>