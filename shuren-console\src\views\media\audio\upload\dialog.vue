<template>
  <el-dialog
      title="上传音频"
      :visible.sync="visible"
      class="media-upload-video"
      :class="{submitting: disabledEdit}"
      width="600px"
      :before-close="beforeClose"
      @closed="closed"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
    <el-form ref="form" :model="model" :rules="rules" size="small" label-width="100px" auto-complete="off" style="margin-right:40px">
      <el-form-item label="原始文件" prop="file_url">
        <el-input v-model="model.file_url" @change="inputUrl" maxlength="128" class="original-url"
                  placeholder="仅支持.mp3结尾的文件或链接"
        ></el-input>
        <div class="btn-up">
          <el-button type="primary">选择</el-button>
          <input type="file" accept=".mp3" multiple @change="inputFile">
        </div>
      </el-form-item>

      <el-form-item label="音频名称" prop="title">
        <el-input v-model="model.title" maxlength="64" placeholder="请输入"></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="12">
          <el-form-item label="音频时长" prop="duration" title="解析后自动获取">
            <el-input :value="duration" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件大小" prop="file_size" title="解析后自动获取">
            <el-input :value="fileSize" readonly></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item style="margin-bottom:0">
        <audio ref="audio" controls style="width:100%"></audio>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible=false" size="small" :disabled="disabledCancel">取 消</el-button>
      <el-button type="primary" @click="onSubmit" :disabled="disabledSubmit" size="small">{{ message }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      message: '提交',
      isUrl: 0,
      status: '',
      model: {
        type: 'audio',
        title: '',
        author: '',
        file_url: '',
        file_size: 0,
        duration: 0
      },
      rules: {
        file_url: {required: true, message: '请填写或上传', trigger: 'blur'},
        title: {required: true, message: '必填项', trigger: 'blur'},
        duration: {type: 'number', required: true, min: 1, message: '错误', trigger: 'blur'},
        file_size: {type: 'number', required: true, min: 1, message: '错误', trigger: 'blur'},
      }
    }
  },
  computed: {
    duration() {
      return this.$utils.durationToTime(this.model.duration);
    },
    fileSize() {
      return this.$utils.bytesToSize(this.model.file_size);
    },
    disabledSubmit() {
      return ['success', 'wait upload', 'upload failed', 'wait submit', 'failed'].indexOf(this.status) == -1;
    },
    disabledCancel() {
      return ['parsing', 'uploading', 'submitting'].indexOf(this.status) != -1;
    },
    disabledEdit() {
      return ['parsing', 'uploading', 'submitting'].indexOf(this.status) != -1;
    }
  },
  beforeDestroy() {
    this.unload(true);
  },
  methods: {
    beforeClose(done) {
      this.disabledCancel || done();
    },
    closed() {
      this.$emit('closed');
      requestAnimationFrame(() => {
        this.$destroy();
        this.$el.remove();
      });
    },
    getProxyUrl(url) {// 视频代理
      return url;
    },
    testUrl(str) {
      return /^http(s)?:\/\/.*\.mp3$/.test(str);
    },
    invalidExt() {
      this.onError('仅支持.mp3结尾的文件');
    },
    onError(msg) {
      this.status = 'error';
      this.$notify.error({title: '错误', message: msg});
    },
    inputUrl(url) {// 手动输入原始视频播放地址
      this.files = null;
      this.isUrl = this.testUrl(url);

      Object.assign(this.model, {
        file_url: this.isUrl ? this.getProxyUrl(url) : '',
        file_size: 0,
        duration: 0
      });

      if (this.isUrl) {
        this.parseAudio(url);
      } else {
        this.parseAudio();
        this.invalidExt();
      }
    },
    inputFile(e) {
      let mp3 = e.target.files[0];
      let model = Object.assign(this.model, {
        file_url: mp3.name,
        file_size: mp3.size
      });

      if (!model.title || model.title == model.file_url) model.title = mp3.name.replace('.mp3', '');

      this.$refs.form.clearValidate();
      this.isUrl = 0;
      this.files = mp3;
      this.parseAudio(URL.createObjectURL(mp3), 'mp3');

      e.target.value = '';
    },
    parseAudio(url, type) {
      this.unload(true);
      this.$refs.form.clearValidate();

      let model = Object.assign(this.model, {width: 0, height: 0});

      if (!url) return;

      type = type || url.substr(url.lastIndexOf('.') + 1);

      if (type != 'mp3') {
        return this.invalidExt();
      }

      this.status = 'parsing';
      this.message = '解析中';

      let scope = this, audio = this.$refs.audio;
      let removeAudio = function (err) {
        scope.unload();

        if (err) {
          scope.message = '解析失败';
          scope.onError(err);
        } else if (!scope.isUrl && model.file_size > 1024 * 1024 * 50) {
          scope.onError('上传文件不能超过50M，请自行压缩！');
        } else if (model.file_size > 0 && model.duration > 0) {
          scope.status = scope.isUrl ? 'wait submit' : 'wait upload';
          scope.message = scope.isUrl ? '提交' : '上传';
        }
      }

      let onload = function () {
        model.duration = this.duration;

        scope.unload();

        // 获取网络文件的大小
        if (!scope.isUrl) return removeAudio();

        scope.getFileSize(url, type).then(function (size) {
          model.file_size = size;
          removeAudio();
        }).catch(function (e) {
          removeAudio(e.message || e);
        });
      };

      //audio.muted = true;
      audio.autoplay = true;
      audio.onloadeddata = onload;
      audio.oncanplaythrough = onload;
      audio.onloadedmetadata = onload;
      audio.onerror = function (e) {
        if (!!audio.currentSrc) {
          removeAudio('若无法正确获取时长将不可上传');
        }
      };

      audio.setAttribute('src', url);
      audio.play();
    },
    getFileSize(url) {
      return this.$api.media.head(url).then(res => {
        if (res['content-type'].startsWith('audio') || res['content-type'] == 'application/octet-stream') {
          return Number(res['content-length']);
        } else {
          return Promise.reject('文件格式错误');
        }
      });
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        switch (this.status) {
          case 'wait upload':
          case 'upload failed':
            return this.doUpload();
          case 'success':
            this.visible = false;
            return;
          case 'wait submit':
          case 'failed':
            return this.doSubmit();
        }
      });
    },
    doUpload() {
      this.status = 'uploading';
      let {model, files} = this;

      this.$api.media.upload.request({
        type: 'audio',
        size: model.file_size,
        width: model.width,
        height: model.height,
        duration: model.duration
      }, files, (p) => {
        this.message = '已上传' + p + '%';
      }).then(res => {
        this.model.file_url = res.url;
        this.doSubmit();
      }).catch(e => {
        this.status = 'upload failed';
        this.message = '上传失败';
      });
    },
    doSubmit() {
      this.status = 'submitting';
      this.message = '提交中';

      let {model} = this;
      model.address = [];
      model.lower_right_corner = this.duration;
      // this.$set(model,"sub_users",[10004078,10004077])
      this.$api.media.save(model).then(media => {
        this.status = 'success';
        this.message = '成功(3s)';
        let index = 3;
        let timer = setInterval(() => {
          this.message = '成功(' + index + 's)';
          if (--index < 0) {
            clearInterval(timer);
            this.visible = false;
          }
        }, 1000);
        this.$emit('upload', media);
      }).catch(e => {
        this.message = e.message || e.toString();
        setTimeout(() => this.status = 'failed', 3000);
      });
    },
    unload(clear) {
      let el = this.$refs.audio;

      el.onerror = null;
      el.onloadeddata = null;
      el.oncanplaythrough = null;
      el.onloadedmetadata = null;

      clear && el.setAttribute('src', '');
    }
  }
}
</script>
