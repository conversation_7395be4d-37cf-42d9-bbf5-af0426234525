<template>
  <view-dialog v-if="show" :visible.sync="visible"></view-dialog>
</template>

<script>
import ViewDialog from "./dialog";

export default {
  props: ["visible"],
  components: { ViewDialog },
  template: "<template></template>",
  data() {
    return {
      show: false,
    };
  },
  watch: {
    visible(v) {
      if (v) {
        this.show = true;
      } else {
        this.show = false;
      }
    },
  },
};
</script>