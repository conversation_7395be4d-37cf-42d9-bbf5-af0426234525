<template>
  <div class="table-container" v-loading="loading">
    <slot name="toolbar"></slot>
    <el-table ref="table" size="small" :data="list" row-key="id" tooltip-effect="dark" :height="height"
              highlight-current-row @selection-change="setSelectionChange" default-expand-all @row-click="clickChange">
      <slot></slot>
    </el-table>

    <div class="table-pagination" v-if="pagination && total > 0">
      <el-pagination :total="total" :page-size="limit" :current-page="page" :page-sizes="[10, 15, 30, 50]"
                     @size-change="onSize" @current-change="refresh" layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "mytable",
  props: {
    api: {},
    query: {
      type: Object,
      default: {},
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    pagination: {
      type: <PERSON>olean,
      default: true,
    },
    list: {
      type: Array,
      default: [],
    },
    limit: {
      type: Number,
      default: 30,
    },
    check: {
      type: Array,
      default: [],
    }
  },
  data() {
    return {
      counter: 0,
      page: 1,
      total: 0,
      loading: false,
      currentRow: null,
      height: undefined,
      params: {},
      multipleSelection: [],
      defaultSelect: [],
    };
  },

  mounted() {
    this.listenerForm();
    this.autoLoad && this.refresh(true);
    this.toggleSelection();
  },
  activated() {
    (this.autoLoad && this.loading) || this.refresh();
  },
  watch: {},
  methods: {
    onSize(val) {
      // console.log(val);
      this.limit = val;
      this.refresh(this.query);
    },
    request(q) {
      if (this.check.length) {
        q.user_tag = this.check.toString()
      }
      console.log(q);
      let { api } = this;
      if (!api) {
        this.loading = false;
        return Promise.reject('未传递api地址，无影响');
      }
      return typeof api == "string" ? this.$api.get(api, q) : api(q);
    },
    refresh(page) {

      this.loading = true;
      let query = this.query,
        counter = ++this.counter,
        limit = this.limit;

      if (!page) {
        query = this.params;
      } else if (page === true) {
        this.params = query = this.getFormValue();
      } else if (/^\d+$/.test(page)) {
        query = Object.assign(query, { offset: (page - 1) * limit, limit });
      } else {
        // 临时
        query = Object.assign({ offset: 0, limit: limit }, page);
      }
      this.request(query)
        .then((res) => {
          if (counter != this.counter) return;
          let rows,
            total,
            singlePage = Array.isArray(res),
            { list } = this,
            page = parseInt(query.offset / query.limit) + 1;
          list.length = 0;
          if (singlePage) {
            rows = res;
            total = res.length;
          } else {
            rows = res.list || res.rows;
            total = res.total || res.count;
          }
          if (rows.length == 0) {
            this.list = [];
            this.$nextTick(() => {
              this.list = list;
            });
          } else {
            rows.forEach((item) => list.push(item));
            this.list.forEach((v, i) => {
              if (v.id && v.id == 5904) {
                this.defaultSelect.push(v);
                this.toggleSelection(this.defaultSelect);
              }
            });
          }
          this.total = total;
          this.page = page;
        })
        .finally((_) => {
          if (counter == this.counter) {
            this.loading = false;
          }
        });
    },
    getFormValue() {
      let { query } = this,
        params = {},
        value;
      for (let field in query) {
        if (/^[a-zA-Z]\w+$/.test(field)) {
          value = query[field];
          if (value !== null && value != undefined) {
            params[field] = Array.isArray(value) ? value.join(",") : value;
          }
        }
      }
      if (this.pagination)
        Object.assign(params, { offset: 0, limit: this.limit });

      return params;
    },
    onSubmitForm(e) {
      e.stopPropagation();
      e.preventDefault();
      this.refresh(true);
    },
    listenerForm() {
      let form = this.$el.querySelector("form");
      if (!form) return;

      let btn = form.querySelector('button[type="submit"]');
      if (!btn) {
        btn = document.createElement("button");
        btn.type = "submit";
        btn.style.cssText = "display:none!important";
        form.appendChild(btn);
      }

      form.addEventListener("submit", this.onSubmitForm);

      btn = form.querySelector(".el-icon-search");
      if (btn) btn.addEventListener("click", this.onSubmitForm);
    },
    setSelectionChange(row) {
      this.multipleSelection = row;
      this.$emit("selectfunc", this.multipleSelection);
    },
    clickChange(row) {
      this.$emit('rowClick', row)
    },

    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.table.toggleRowSelection(row);
        });
      } else {
        this.$refs.table.clearSelection();
      }
    },
  },
};
</script>

<style lang="scss">
.el-table__indent {
  position: relative;
  height: 1px;
  display: inline-block;

  &:before {
    content: "";
    border-top: 1px dashed #bababa;
    display: block;
    margin-top: -4px;
    width: 100%;
    position: absolute;
    left: 14px;
  }

  &:after {
    content: "";
    border-left: 1px dashed #bababa;
    display: block;
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    height: 40px;
    transform: translateY(-50%);
  }
}

.el-table__expand-icon .el-icon-arrow-right {
  background: #fff;
}

.el-table__row.current-row .el-table__expand-icon .el-icon-arrow-right {
  background: #ecf5ff;
}

.el-table__row:hover .el-table__expand-icon .el-icon-arrow-right {
  background: #f5f7fa;
}

.table-toolbar {
  margin-bottom: 16px;
}

.toolbar-header {
  display: flex;
  align-items: center;
}

.toolbar-header .toolbar-title {
  flex: 1;
  line-height: 32px;
}

.toolbar-header .toolbar-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.toolbar-header .toolbar-actions>* {
  margin-left: 10px;
}

.table-container {
  .search-form {
    background: #f7f8fa;
    padding: 20px 0;
    margin-bottom: 18px;
  }

  .search-item {
    display: flex;
    flex-wrap: wrap;
    min-width: 290px;
    max-width: 1100px;

    .el-form-item:first-child {
      width: 500px;
    }

    .el-form-item {
      width: 400px;
    }

    .el-date-editor,
    .el-cascader,
    .el-select,
    .el-input {
      width: 100%;
    }
  }
}

.el-table .cell {
  white-space: nowrap;
}

.table-container {
  background: #fff;
}

.table-container thead th+th.is-center .cell,
.table-container tbody td+td.is-center .cell {
  padding: 0;
}

.table-pagination {
  text-align: center;
  background-color: #fff;
  padding: 20px 0 0 0;
}

.el-table .el-button--mini,
.el-table .el-button--mini.is-round {
  padding: 5px 15px;
}

.table-input {
  padding: 0 !important;
}

.table-input .cell {
  position: static;
}

.table-input input {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border: none;
  outline: none;
}

.el-table .table-input input:hover,
.el-table .current-row .table-input input {
  background-color: #ecf5ff;
}

.el-icon-search {
  cursor: pointer;
}

.el-table .table-action,
.el-table .table-action .cell {
  padding: 0;
  text-align: center;
}

.el-table .btn-action {
  border: none;
  margin-left: 0;
}

.table-container .el-table__row .el-input--mini .el-input__inner {
  height: 22px;
  line-height: 22px;
}
</style>