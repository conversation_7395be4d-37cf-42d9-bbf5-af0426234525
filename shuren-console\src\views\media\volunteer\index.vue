<template>
    <div>
      <mytable ref="table" :list="list" :api="$api.media.channel.getAllList" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <!-- <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template> -->
            </div>
          </div>
        </div>
  
        <el-table-column label="姓名" prop="name" align="center" ></el-table-column>
        <el-table-column label="身份证号" prop="id_card" align="center" ></el-table-column>
        <el-table-column label="手机号" prop="mobile" align="center" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" @click.stop="audit(scope.row,'2')" title="通过">
                通过
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="audit(scope.row,'1')" title="不通过">
                不通过
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row)" title="查看">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button>
              <!-- <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
                删除
              </el-button> -->
            </template>
          </template>
        </el-table-column>
      </mytable>


      <el-dialog title="志愿者信息" :visible.sync="visible" class="media-upload-video"
                 width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
                 :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules"  size="small" label-width="150px" auto-complete="off"
                 style="margin-right:40px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="model.name" disabled></el-input>
          </el-form-item>
          <el-form-item label="民族" prop="group">
            <el-input v-model="model.group" disabled ></el-input>
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input v-model="model.age" disabled></el-input>
          </el-form-item>
          <el-form-item label="身份证" prop="id_card">
            <el-input v-model="model.id_card"  disabled></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">  
            <el-input v-model="model.mobile" disabled></el-input>
          </el-form-item>
          <el-form-item label="家庭住址" prop="family_address">
            <el-input v-model="model.family_address" disabled></el-input>
          </el-form-item>
          
          <el-form-item label="申请时间" prop="created">
            <el-input v-model="model.created" disabled></el-input>
          </el-form-item>
        </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary"  size="small" @click="cancel">关闭</el-button>
        </div>
      </el-dialog>

      <el-dialog title="志愿者信息" :visible.sync="child_visible" class="media-upload-video"
                 width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
                 :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules"  size="small" label-width="150px" auto-complete="off"
                 style="margin-right:40px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="model.name" disabled></el-input>
          </el-form-item>
          <el-form-item label="身份证" prop="id_card">
            <el-input v-model="model.id_card"  disabled></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">  
            <el-input v-model="model.mobile" disabled></el-input>
          </el-form-item>
          <el-form-item label="监护人姓名" prop="parent_name">
            <el-input v-model="model.parent_name" disabled></el-input>
          </el-form-item>
          <el-form-item label="监护人身份证" prop="parent_id_card">
            <el-input v-model="model.parent_id_card"  disabled></el-input>
          </el-form-item>
          <el-form-item label="监护人手机号" prop="parent_mobile">  
            <el-input v-model="model.parent_mobile" disabled></el-input>
          </el-form-item>
          <el-form-item label="申请时间" prop="created">
            <el-input v-model="model.created" disabled></el-input>
          </el-form-item>
        </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary"  size="small" @click="cancel_child">关闭</el-button>
        </div>
      </el-dialog>


    </div>
  </template>
    
  <script>

  export default {
    data() {
      return {
        query:{
          audit:'0'
        },
        child_visible:false,
        visible:false,
        list:[],
        model:{}
      };
    },
    methods: {
      refresh() {
        this.$refs.table.refresh();
      },
      audit(v, status){
        // 1不通过  2通过
        let audit = status
        if(status == '1'){
            this.$confirm("操作不可恢复，确定要不通过吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              console.log(v);
              if(v.open_id){
                return this.$api.post('/v1/mini.volunteers.audit', { open_id: v.open_id,audit: audit });
              }else{
                return this.$api.post('/v1/mini.volunteers.child.audit', { parent_open_id: v.parent_open_id,audit: audit });
              }
            })
            // .then(this.refresh);
        }else{
            this.$confirm("操作不可恢复，确定通过吗?通过后将自动发放1积分", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              console.log(v);
              axios.get('https://platform.wumengyoupin.com/open/api/user/userpoint/updatepoint', {
                 params:{
                  wxopenid:v.open_id,
                  change:1,
                  reason:'申请志愿者成功'
                 },
                 headers: {
                      'Content-Type': 'application/json',
                      'apptoken': 'f730b1a19581d04c45fad9adbc925350'
                  },
              }).then(response => {
                  console.log('success', response.data)
              }, error => {
                  console.log('错误', error.message)
              })
              if(v.open_id){
                return this.$api.post('/v1/mini.volunteers.audit', { open_id: v.open_id,audit: audit });
              }else{
                return this.$api.post('/v1/mini.volunteers.child.audit', { parent_open_id: v.parent_open_id,audit: audit });
              }
            })
            .then(this.refresh);
        }
       
      },
      onDel(v){
        this.$api.post('/v1/mini.volunteers.delete', { id: v.id });
        // this.$api.post('/v1/mini.volunteers.child.delete', { id: v.id });
      },
     async onDetail(v){
        if(v.open_id){
          let res  = await this.$api.get('/v1/mini.volunteers.query', { open_id: v.open_id });
          this.visible = true
          this.model = res
        }else{
          let res  = await this.$api.get('/v1/mini.volunteers.child.query', { parent_open_id: v.parent_open_id });
          this.child_visible = true
          this.model = res
        }

      },
      cancel(){
        this.visible = false
      },
      cancel_child (){
        this.child_visible = false
      }
      
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  </style>