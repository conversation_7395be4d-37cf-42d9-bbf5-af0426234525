<template>
    <div>
      <mytable ref="table" :list="list" :api="'/v1/mini.passbook.pendingList'" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <!-- <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template> -->
            </div>
          </div>
        </div>
  
        <el-table-column label="姓名" prop="name" align="center" ></el-table-column>
        <el-table-column label="手机号" prop="phone" align="center" ></el-table-column>
        <el-table-column label="地址" prop="address" align="center" width="250" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="190px">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row,1)" >
                通过
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row,2)" >
                拒绝
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row)" title="查看">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>


      <el-dialog title="详细信息" :visible.sync="visible" class="media-upload-video"
                 width="900px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
                 :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules"  size="small" label-width="150px" auto-complete="off"
                 style="margin-right:40px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="model.name" ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">  
            <el-input v-model="model.phone" ></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input v-model="model.address" ></el-input>
          </el-form-item>
          <el-form-item label="文字解说" prop="text">
            <el-input v-model="model.text" type="textarea" ></el-input>
          </el-form-item>
          <!-- 图片区 -->
        <div v-if="imageList.length > 0" class="media-group">
          <div class="media-label">图片</div>
          <div class="media-preview">
            <el-image
              v-for="(media, index) in imageList"
              :key="'img' + index"
              :src="media.media_url"
              :preview-src-list="[media.media_url]"
              fit="contain"
              style="width: 120px; height: 120px; border: 1px solid #eee;"
            ></el-image>
          </div>
        </div>

        <!-- 视频区 -->
        <div v-if="videoList.length > 0" class="media-group">
          <div class="media-label">视频</div>
          <div class="media-preview">
            <video
              v-for="(media, index) in videoList"
              :key="'video' + index"
              :src="media.media_url"
              controls
              style="max-width: 100%; margin-bottom: 10px; border: 1px solid #ccc;"
            ></video>
          </div>
        </div>
        
        
        

        <!-- 音频区 -->
        <div v-if="audioList.length > 0" class="media-group">
          <div class="media-label">音频</div>
          <div class="media-preview">
            <audio
              v-for="(media, index) in audioList"
              :key="'audio' + index"
              :src="media.media_url"
              controls
              style="width: 100%; margin-bottom: 10px;"
            ></audio>
          </div>
        </div>
        

        </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary"  size="small" @click="cancel">关闭</el-button>
        </div>
      </el-dialog>

      <el-dialog title="积分发放" :visible.sync="visible_point" width="30%">
        <p>提示：审核通过后，将发放积分给用户，请选择发放的积分值</p>
          
        <el-form ref="form" :model="form" label-width="1px">
          <el-form-item label="" prop="point">
            <el-radio-group v-model="form.point" >
              <el-radio label="0.5">0.5</el-radio>
              <el-radio label="1">1</el-radio>
              <el-radio label="2">2</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer"> 
            <el-button type="primary" @click="submit">确定</el-button>
            <el-button @click="visible_point = false">取消</el-button>
        </div>
      </el-dialog>


    </div>
  </template>
    
  <script>

  export default {
    data() {
      return {
        query:{
        },
        visible_point:false,
        visible:false,
        list:[],
        model:{},
        form:{
            point:"0.5"
        }
      };
    },
    computed: {
  imageList() {
    return (this.model.mediaList && this.model.mediaList.filter(item => item.media_type === 'image')) || [];
  },
  videoList() {
    return (this.model.mediaList && this.model.mediaList.filter(item => item.media_type === 'video')) || [];
  },
  audioList() {
    return (this.model.mediaList && this.model.mediaList.filter(item => item.media_type === 'audio')) || [];
  }
},
    methods: {
        refresh() {
            this.$refs.table.refresh();
        },
        onDel(v){
            this.$confirm("操作不可恢复，确定删除吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                console.log(v);
                return this.$api.post('/v1/mini.soldier.delete', { id: v.id })
                })
                .then(this.refresh);
            
        },
        async onDetail(v, status){
            this.model = v
            if(status) { 
                this.model.status = status
                if(status == 1) { 
                    this.visible_point = true
                }else{
                    this.$confirm("操作不可恢复，拒绝后将删除该条记录。确定拒绝吗？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(() => {
                          return this.$api.post('/v1/mini.passbook.update', this.model)
                        })
                        .then(()=> {
                          return this.$api.post('/v1/mini.passbook.delete', { id: v.id })
                        })
                        .then(this.refresh);
                }

            }else{
                this.model = v
                this.visible = true
            }
        

        },
        
        cancel(){
            this.visible = false
        },
        submit(){
            let that = this
            console.log({...this.model, pont:this.form.point});
            this.$api.post('/v1/mini.passbook.review', {...this.model, point:this.form.point})
            .then(this.refresh)
            .then(() => {
                that.visible_point = false
            })
        }
      
    },
  };
  </script>
    <style>
/* 在全局或当前组件的 <style> 中添加 */
.media-upload-video .el-dialog__body {
  max-height: 60vh; /* 控制最大高度为视口的60% */
  overflow-y: auto; /* 垂直滚动 */
  padding: 20px; /* 保持原有内边距 */
}

/* 可选：调整标题和底部按钮区域的样式 */
.media-upload-video .el-dialog__header,
.media-upload-video .el-dialog__footer {
  padding: 15px 20px;
  background: #fff;
  position: sticky; /* 固定标题和底部（可选） */
  z-index: 10;
}
.media-upload-video .el-dialog__header {
  top: 0;
  border-bottom: 1px solid #eee;
}
.media-upload-video .el-dialog__footer {
  bottom: 0;
  border-top: 1px solid #eee;
}
</style>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  .media-group {
  margin-bottom: 20px;
  text-align: center;
}

.media-label {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.media-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}
  
  </style>