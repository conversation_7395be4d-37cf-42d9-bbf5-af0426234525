<template>
  <div class="media-table-container">
    <media-channel v-model="query.channel" :all="true"></media-channel>

    <mytable ref="table" :api="$api.media.list" :query="query" :list="list">
      <el-form class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}
            <!--
            <el-popover
                placement="bottom-start"
                title="快速定位"
                width="300"
                trigger="hover">
              <el-input v-model="link" size="mini" placeholder="请粘贴资源详情页地址"></el-input>
              <div style="text-align:right;margin-top:10px">
                <el-button type="primary" size="mini" @click="onLink">查找</el-button>
              </div>
              <el-button slot="reference" type="primary" size="mini" circle plain>
                <svg aria-hidden="true" class="icon">
                  <use xlink:href="#dingwei"></use>
                </svg>
              </el-button>
            </el-popover>
            -->
          </div>
          <div class="toolbar-actions">
            <el-date-picker
                title="发布时间"
                v-model="pubdate"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="timestamp"
                align="right"
                size="mini"
                :default-time="['00:00:00', '23:59:59']"
                @change="onPubdate">
            </el-date-picker>
            <media-type v-model="query.type" style="width:100px" size="mini"></media-type>
            <el-input title="资源名称" class="search-val"
                      size="mini" v-model="query.kw" placeholder="资源标题"
                      style="width:100px" maxlength="5" clearable>
            </el-input>
            <el-button size="mini" icon="el-icon-search" native-type="submit" title="搜索" circle></el-button>
            <el-button type="primary" size="mini" icon="el-icon-upload" @click="onAdd" title="创建" circle></el-button>
          </div>
        </div>
      </el-form>

      <el-table-column prop="id" label="编号" width="75" align="center" :formatter="formatter"></el-table-column>
      <el-table-column label="标题" prop="title"></el-table-column>
      <el-table-column label="类型" prop="_type" width="80" align="center" v-if="!query.type"></el-table-column>
      <el-table-column prop="_status" label="状态" align="center" width="80"></el-table-column>
      <el-table-column prop="_pubdate" label="发布时间" align="center" width="136"></el-table-column>
      <el-table-column prop="pv" label="点击量" align="center" width="70"></el-table-column>
      <el-table-column label="操作" width="60" align="center" class-name="table-action">
        <div slot-scope="scope" class="action">
          <el-dropdown @command="command($event, scope.row)">
            <span class="el-dropdown-link">
               <el-button class="btn-action" size="mini">
                 <i class="el-icon-setting"></i>
               </el-button>
            </span>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
                <span>编辑</span>
              </el-dropdown-item>
              <el-dropdown-item command="share">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#fenxiang"></use>
                </svg>
                <span>分享</span>
              </el-dropdown-item>
              <el-dropdown-item command="preview">
                <i class="el-icon-view"></i><span>预览</span>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.status=='online'" command="offline">
                <i class="el-icon-download"></i><span>下架</span>
              </el-dropdown-item>
              <el-dropdown-item v-else command="online">
                <i class="el-icon-position"></i>
                <span>发布</span>
              </el-dropdown-item>
              <el-dropdown-item command="delete">
                <i class="el-icon-delete"></i><span>删除</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-table-column>
    </mytable>
  </div>
</template>

<script>
import MediaBase from './base';
import MediaChannel from '../channel/components/tree';

export default {
  name: 'MediaAllPage',
  extends: MediaBase,
  components: {MediaChannel},
  computed: {
    list() {
      return []
    }
  },
  watch: {
    'query.channel'(){
      this.$refs.table.refresh(true);
    }
  },
  methods: {
    onCreated() {

    },
    formatter(row) {
      row._type = this.$api.media.type[row.type];
      row._status = this.$api.media.status[row.status];
      row._pubdate = this.$utils.Date(row.pubdate).format();
      return row.id;
    },
    refresh() {
      this.$refs.table.refresh();
      this.needRefresh = 0
    },
    command(e, row) {
      switch (e) {
        case 'edit':
          return this.edit(row);
        case 'share':
          return this.onShare(row);
        case 'discuss':
          return this.onDiscuss(row);
        case 'preview':
          return this.onPreview(row);
        case 'offline':
          return this.offline(row);
        case 'online':
          return this.online(row);
        case 'cancel':
          return this.cancel(row);
        case 'delete':
          return this.delete(row);
        case 'liveAddress':
          return this.liveAddress(row);
        case 'videoAddress':
          return this.showVideoAddress(row);
      }
    },
    doLink(id, type) {
      this.$refs.table.refresh({id: id, type: type});
    }
  }
}
</script>