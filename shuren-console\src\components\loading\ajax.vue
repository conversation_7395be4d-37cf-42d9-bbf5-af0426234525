<template>
  <div class="ajax-loading" v-loading="showLoading" @click="showLoading=true"></div>
</template>

<style>
  .ajax-loading{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
  }
</style>

<script>
  export default {
    data(){
      return {
        showLoading: false,
        timer: 0,
        index: 0
      }
    },
    methods: {
      show(){
        this.index++;
        clearTimeout(this.timer);

        document.body.appendChild(this.$el);
        this.timer = setTimeout(() => {
          this.showLoading = true;
        }, 3000);
      },
      hide(){
        if(--this.index < 1){
          this.index = 0;

          clearTimeout(this.timer);
          this.$el.remove();
        }
      }
    }
  }
</script>