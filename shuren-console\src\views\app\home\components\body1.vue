<template>
  <div class="home-editor-body">
    <editor-view v-for="model in list" :model="model"></editor-view>
    <div class="body1PosBox" v-show="this.$store.state.admin.isSpecial && !$store.state.admin.pid">
      <div class="body1Backround">
        <vue-seamless-scroll :data="textList" :class-option="optionHover" class="body1Seamless-warp">
          <p v-for="(v, i) in textList" :key="i" class="body1TopText">
            <span class="nameText">{{ v.title }}</span>
            <span class="timeText">{{ v.alias }}</span>
          </p>
        </vue-seamless-scroll>
      </div>
      <div class="body1Bot">资讯汇聚</div>
      <!-- <img src="http://cdn.zn.nextv.show/img/20230614/35151357/962.png" alt="" class="body1Img" /> -->
    </div>
  </div>
</template>

<script>
import Base from "./body";
import request from "@/api/modules/request.js"
import vueSeamlessScroll from "vue-seamless-scroll";
export default {
  extends: Base,
  data() {
    return {
      status: false,
      list: [],
      textList: [],
    };
  },
  components: { vueSeamlessScroll },
  computed: {
    optionHover() {
      return {
        step: 0.4, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  created() {
    // this.list =
    //   this.model.theme == 1
    //     ? this.model.body
    //     : 
    // true不是独立账户  false独立账户
    if (this.$store.state.admin.isSpecial) {
      if (this.$store.state.admin.pid) {
        this.list = this.model.theme == 1 ? this.model.body :
          [
            {
              name: "rect",
              width: 370,
              height: 562,
              x: 0,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            {
              name: "video",
              width: 1000,
              height: 562,
              x: 400,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            // {
            //   name: "rect1",
            //   width: 370,
            //   height: 562,
            //   x: 1430,
            //   y: 0,
            //   r: 0,
            //   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //   border: {
            //     enabled: 1,
            //     width: 0,
            //     style: "none",
            //     color: "none",
            //     radius: 10,
            //   },
            //   background: {
            //     color: "rgba(0,0,0,0.2)",
            //     opacity: 0.2,
            //     image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
            //     size: "cover",
            //   },
            // },
            {
              name: "rect",
              width: 370,
              height: 562,
              x: 1430,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                // opacity: 0.2,
                image: "",
                size: "cover",
              },
            },
          ];

      } else {
        this.list = this.model.theme == 1 ? this.model.body :
          [
            {
              name: "rect2",
              width: 370,
              height: 562,
              x: 0,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            {
              name: "video",
              width: 1000,
              height: 562,
              x: 400,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
            },
            {
              name: "rect1",
              width: 370,
              height: 562,
              x: 1430,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                opacity: 0.2,
                image: "http://cdn.zn.nextv.show/img/20230614/35151357/962.png",
                size: "cover",
              },
            },
            // {
            //   name: "rect",
            //   width: 370,
            //   height: 562,
            //   x: 1430,
            //   y: 0,
            //   r: 0,
            //   shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            //   border: {
            //     enabled: 1,
            //     width: 0,
            //     style: "none",
            //     color: "none",
            //     radius: 10,
            //   },
            //   background: {
            //     color: "rgba(0,0,0,0.2)",
            //     // opacity: 0.2,
            //     image: "",
            //     size: "cover",
            //   },
            // },
          ];
      }

    } else {
      this.list = this.model.theme == 1 ? this.model.body :
        [
          {
            name: "rect",
            width: 370,
            height: 562,
            x: 0,
            y: 0,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              image: "",
              size: "cover",
            },
          },
          {
            name: "video",
            width: 1000,
            height: 562,
            x: 400,
            y: 0,
            r: 0,
            loop: 1,
            address: "",
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              image: "",
              size: "cover",
            },
          },
          {
            name: "rect",
            width: 370,
            height: 562,
            x: 1430,
            y: 0,
            r: 0,
            shadow: { x: 0, y: 0, b: 20, color: "#000000" },
            border: {
              enabled: 1,
              width: 0,
              style: "none",
              color: "none",
              radius: 10,
            },
            background: {
              color: "rgba(0,0,0,0.2)",
              // opacity: 0.2,
              image: "",
              size: "cover",
            },
          },
        ];
    }

    if (!this.$store.state.admin.pid) {
      request.get('/v1/media.item.subList', { creator: this.$store.state.admin.id }).then((res) => {
        this.textList = res
      }).catch(err => {
        console.log(err);
      })
    } else {
      request.get('/v1/media.item.pList').then((res) => {
        this.textList = res
      }).catch(err => {
        console.log(err);
      })
    }

  },
};
</script>
<style lang="scss">
.home-editor-bod {
  position: relative;
}

.body1PosBox {
  width: 370px;
  height: 562px;
  position: fixed;
  top: 190px;
  right: 60px;
  border-radius: 10px;
}

.body1Img {
  width: 370px;
  height: 562px;
  opacity: 0.1;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
}

::-webkit-scrollbar {
  display: none;
}

.body1TopText {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-around;
  padding: 0 20px;
  line-height: 40px;
  z-index: 99;
}

.body1NameText {
  color: #fdf451;
  width: 30%;
  height: 100%;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: left;
}

.body1TitleText {
  color: #f1f1f1;
  width: 40%;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: center;
}

.body1TimeText {
  color: white;
  font-size: 20px;
  width: 30%;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: right;
}

.body1Bot {
  width: 100%;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 10px 10px;
  font-size: 25px;
  color: white;
  text-align: center;
  line-height: 50px;
  z-index: 99;
}

.body1Backround {
  width: 370px;
  height: 562px;
  border-radius: 10px;
  padding-bottom: 30px;
  overflow: auto;
  background: url(http://cdn.zn.nextv.show/img/20230614/35151357/962.png) no-repeat;
  background-size: 100% 100%;

}

.body1Seamless-warp {
  height: 562px;
  overflow: hidden;
}
</style>
