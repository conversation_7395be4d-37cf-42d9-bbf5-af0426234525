import SelectImage from "@/views/media/image/select";
import SelectVideo from "@/views/media/video/select";

export default {
  props: ["value", "height"],
  data() {
    return {
      id: "editor" + Date.now(),
      loading: true,
    };
  },
  watch: {
    value(v) {
      console.log("触发了");
      this.setData(v);
    },
  },
  mounted() {
    if (typeof window.tinymce == "undefined") {
      this.$require("tinymce").then(() => {
        this.initPlugin();
        this.initEditor();
      });
    } else {
      this.initEditor();
    }
  },
  activated() {
    this.loading || this.initEditor();
  },
  deactivated() {
    this.remove();
  },
  beforeDestroy() {
    this.remove();
  },
  methods: {
    initPlugin() {
      // 图片插件
      let scope = this,
        tinymce = window.tinymce;

      function showImageDialog(editor) {
        SelectImage(8).then((list) => {
          list.forEach(function (data) {
            editor.insertContent(
              '<p><img class="html-img" src="' + data.url + '"></p>'
            );
          });
        });
      }

      tinymce.PluginManager.add("image", function (editor) {
        editor.ui.registry.addButton("image", {
          text: "",
          icon: "image",
          onAction: function () {
            showImageDialog(editor);
          },
        });

        editor.ui.registry.addMenuItem("image", {
          text: "图片",
          icon: "image",
          context: "tools",
          onAction: function () {
            showImageDialog(editor);
          },
        });
      });

      function showVideoDialog(editor) {
        SelectVideo(1).then((list) => {
          let media = list[0],
            src = media.address;
          src = Array.isArray(src)
            ? src[0].mp4 || src[0].m3u8
            : src.mp4 || src.m3u8;
          editor.insertContent(
            `<p><video class="html-video" src="${src}" controls preload="none" poster="${media.cover_url}"></video></p>`
          );

          //let url = scope.$env.APP_URL + '/media/iframe/' + scope.$utils.encodeId(media.id, 'media.' + media.type);
          //editor.insertContent(`<p><iframe class="html-video-iframe" src="${url}" allowfullscreen></iframe></p>`);
        });
      }

      // 视频插件
      tinymce.PluginManager.add("video", function (editor) {
        editor.ui.registry.addIcon(
          "video",
          '<svg viewBox="0 0 1024 1024" width="24" height="24"><path d="M62 118.25v787.5h900V118.25H62z m131.24999971 731.25H118.25v-74.99999971h74.99999971v74.99999971z m0-150.00000029H118.25v-74.99999971h74.99999971v74.99999971z m0-149.99999942H118.25v-75.00000058h74.99999971v75.00000058z m0-150.00000029H118.25v-74.99999971h74.99999971v74.99999971z m0-150.00000029H118.25V174.5h74.99999971v74.99999971z m225 391.87500029V388.25c0-1.87499971 1.87499971-3.75000029 5.625-3.75000029L643.24999971 512l-225 129.375zM905.75 849.5h-74.99999971v-74.99999971h74.99999971v74.99999971z m0-150.00000029h-74.99999971v-74.99999971h74.99999971v74.99999971z m0-149.99999942h-74.99999971v-75.00000058h74.99999971v75.00000058z m0-150.00000029h-74.99999971v-74.99999971h74.99999971v74.99999971z m0-150.00000029h-74.99999971V174.5h74.99999971v74.99999971z"></path></svg>'
        );

        editor.ui.registry.addButton("video", {
          text: "",
          icon: "video",
          onAction: function () {
            showVideoDialog(editor);
          },
        });

        editor.ui.registry.addMenuItem("video", {
          text: "视频",
          icon: "video",
          context: "tools",
          onAction: function () {
            showVideoDialog(editor);
          },
        });
      });
    },
    initEditor() {
      let scope = this;

      this.remove();

      window.tinymce
        .init({
          selector: "#" + this.id,
          language: "zh_CN",
          language_url: this.CDN_URL + "/js/tinymce/langs/zh_CN.js",
          content_style:
            "body{text-align:justify;font-size:3vmin;color:#333;line-height:1.8;outline:none;background:#fff;}" +
            "p{margin:0;padding:0;box-sizing:border-box}" +
            ".html-img{display:block;max-width:100%;margin:0 auto;}" +
            "iframe{width:100%;border:none;margin:0;padding:0;}" +
            ".html-video-iframe{background:#333;width: 100%;height: 0;padding-bottom: 56.25%;position:relative}" +
            ".html-video{width:100%;height:56.25vw;object-fit:cover;}",
          plugins: [
            "autolink",
            "link",
            "lists",
            "table",
            "textcolor",
            "image",
            "video",
            "quickbars",
            "paste",
            "codesample",
          ],
          inline: false,
          menubar: false,
          toolbar:
            "fontsizeselect forecolor backcolor | alignleft aligncenter alignright alignjustify | outdent indent | bold italic underline strikethrough | numlist bullist | codesample | image video link table",
          toolbar_drawer: false,
          quickbars_insert_toolbar: false,
          quickbars_selection_toolbar: false,
          contextmenu:
            "image video link | inserttable | cell row column deletetable",
          powerpaste_word_import: "clean",
          powerpaste_html_import: "clean",
          paste_data_images: true,
          fontsize_formats: "1vmin 2vmin 3vmin 4vmin 5vmin 6vmin 7vmin 8vmin",
          height: this.height,
          valid_classes: "",
          extended_valid_elements:
            "iframe[src|width|height|allowfullscreen|class|style]",
          setup: function (ed) {
            ed.on("keydown", function (e) {
              if (e.code == "Tab") {
                e.preventDefault();
                e.stopPropagation();

                let p = ed.selection.getNode();
                p.style.textIndent = e.shiftKey ? "" : "2em";

                if (
                  !e.shiftKey &&
                  window.getComputedStyle(p, null).display == "inline"
                ) {
                  p.style.display = "inline-block";
                }
              }
            });

            ed.on("ObjectSelected", function (e) {
              console.log(e);
            });
          },
          images_upload_handler: function (blobInfo, success) {
            scope.onPasteImage(blobInfo, success);
          },
          paste_remove_styles_if_webkit: false,
        })
        .then((editors) => {
          this.editor = editors[editors.length - 1];
          this.setData(this.value);
          this.loading = false;
        });
    },
    onPasteImage(blobInfo, success) {
      // 粘贴图片
      let scope = this;
      let file = blobInfo.blob();
      let img = new Image();
      img.src = "data:image/png;base64," + blobInfo.base64();
      img.onload = function () {
        let width = this.naturalWidth;
        let height = this.naturalHeight;
        // if (this.width > 5000 || this.height > 5000) {
        //   return scope.$message.error('压缩失败！原始文件不能超过5000x5000像素');
        // }

        scope.yasuotupian(
          new File([file], file.name),
          {
            quality: 0.7,
            width: width,
            height: height,
          },
          (err, file) => {
            if (err) {
              success("");

              return scope.$notify.error({
                title: "错误",
                dangerouslyUseHTMLString: true,
                message:
                  "<div>" +
                  err +
                  '</div><div><a href="https://tinypng.com/" target="_blank">压缩图片</a></div>',
              });
            }

            file =
              file instanceof File
                ? file
                : new File([file], file.name, { type: file.type });

            scope.$api.media.upload
              .request(
                {
                  type: "image",
                  size: file.size,
                  width: width,
                  height: height,
                },
                file,
                (p) => {
                  console.log("已上传" + p + "%");
                }
              )
              .then((res) => {
                success(res.url);
              });
          }
        );
      };
    },
    calcWH(w, h, m) {
      if (w > m) {
        h = Math.ceil((m / w) * h);
        w = m;
      } else if (h > m) {
        w = Math.ceil((m / h) * w);
        h = m;
      } else {
        return [w, h];
      }
      return this.calcWH(w, h, m);
    },
    yasuotupian(file, params, callback, prevSize) {
      let maxSize = 300,
        wh = 800,
        isYaSuo = file.size > 1024 * maxSize;
      if (prevSize && file.size >= prevSize && isYaSuo) {
        return callback(`图片 ${file.name} 超出 ${maxSize}KB 限制`);
      }

      if (!isYaSuo) {
        if (params.width > wh || params.height > wh) {
          [params.width, params.height] = this.calcWH(
            params.width,
            params.height,
            wh
          );
        } else {
          return callback(null, file);
        }
      }

      this.$require("lrz").then(() => {
        lrz(file, params)
          .then((rst) => {
            if (!rst.file.name) {
              rst.file.name = file.name;
            }

            this.yasuotupian(rst.file, params, callback, file.size);
          })
          .catch((e) => {
            callback(e.message || e);
          });
      });
    },
    getData(text) {
      console.log(text);
      return this.editor
        ? this.editor.getContent(text ? { format: "text" } : undefined)
        : this.value;
    },
    setData(text) {
      console.log(text);
      this.editor && this.editor.setContent(text || "");
    },
    getImages() {
      let container = document.getElementById(this.id + "_ifr").contentDocument;
      let nodes = container.querySelectorAll("img");
      let list = [];
      let exists = [];
      let task = [];

      for (let i = 0, img; i < nodes.length; i++) {
        img = nodes[i];

        task.push(
          new Promise((resolve, reject) => {
            img.onload = () => {
              img.onload = null;
              img.onerror = null;

              if (exists.indexOf(img.src) == -1) {
                exists.push(img.src);

                list.push({
                  url: img.src,
                  size: parseInt(res["content-length"]),
                  width: img.naturalWidth,
                  height: img.naturalHeight,
                });
              }

              resolve();
            };

            img.onerror = () => {
              img.onload = null;
              img.onerror = null;
              reject();
            };

            img.src = img.src;
          })
        );
      }

      return Promise.all(task).then((values) => {
        return list;
      });
    },
    getImgUrl() {
      let container = document.getElementById(this.id + "_ifr").contentDocument;
      let nodes = container.querySelectorAll("img");
      let list = [];

      for (let i = 0, img; i < nodes.length; i++) {
        img = nodes[i];

        if (list.indexOf(img.src) == -1) {
          list.push(img.src);
        }
      }

      return list;
    },
    remove() {
      window.tinymce.remove("#" + this.id);
    },
  },
  render() {
    return <textarea id={this.id}></textarea>;
  },
};
