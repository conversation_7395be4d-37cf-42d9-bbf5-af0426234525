import Share from '../components/share/';
import Discuss from '../components/discuss/';
import MediaType from '../components/type';

export default {
  components: {MediaType},
  data() {
    return {
      query: {kw: '', channel: 0, pubdate: '', type: '', creator: this.$store.state.admin.id},
      userQuery: {audit_status:1,nickname:''},
      auditData: {audit_status:1},   
      unAuditData: {audit_status:0,nickname:''},  
      subUsersQuery: {nickname:''},   
      treeData:{},
      pubdate: [],
      share: {visible: false, data: null},
      reply: {visible: false, topicId: null},
      needRefresh: 0,
      showFast: 0,
      addrDialog: null,
      pickerOptions: this.$utils.pickerOptions,
      link: ''
    }
  },
  created() {
    this.onCreated();
  },
  methods: {
    onAdd() {
      this.$router.push({name: 'media.publish'});
    },
    onChanged() {
      if (this._inactive) {
        this.needRefresh = 1;
      } else {
        this.refresh();
      }
    },
    onPubdate(v) {
      this.query.pubdate = v && parseInt(v[0] / 1000) + '_' + parseInt(v[1] / 1000);
    },
    onShare(item) {
      Share.show(item.id, item.type);
    },
    onDiscuss(item) {
      Discuss.show(item.id);
    },
    onPreview(item) {
      this.$api.media.preview(item.id, item.type, item.version, this.APP_URL);
    },
    online(item) {
      this.$api.media.publish(item.id, item.type, item.draft).then(this.refresh);
    },
    offline(item) {
      this.$confirm('下架后用户无法正常访问此作品，确定下架吗？', '提示', {
        confirmButtonText: '下架',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return this.$api.media.offline(item.id, item.type);
      }).then(this.refresh);
    },
    edit(media) {
      this.$router.push({name: 'media.editor', params: {type: media.type}, query: {id: media.id}});
    },
    delete(item) {
      this.$confirm('操作不可恢复，确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.media.delete(item.id, item.type).then(this.refresh);
      });
    },
    showVideoAddress(item) {
      this.addrDialog = {id: item.id, type: item.type};
    },
    onLink() {
      let uri, id, type, link = this.link;

      if (!link) return;

      try {
        uri = new URL(link);
        id = uri.searchParams.get('id');
        type = uri.searchParams.get('type');

        if (!id || !type) {
          let alias = uri.pathname.match(/\/media\/(detail|iframe)\/(\w+)/);

          if (!alias) throw '非资源详情页地址';

          let res = this.$utils.decodeId(alias[2], 'media');
          if (!res) throw '非资源详情页地址';

          id = res.id;
          type = res.type;
        }
      } catch (e) {
        return this.$message.error('非资源详情页地址');
      }

      this.doLink(id, type);
    }
  }
}