import Base from './base';

export default {
  extends: Base,
  data() {
    return {
      active: 'base'
    }
  },
  computed: {
    current() {
      for (let list = this.menu, i = 0, len = list.length; i < len; i++) {
        if (list[i].name == this.active) {
          return i;
        }
      }
    }
  },
  methods: {
    prev() {
      this.go(this.menu[this.current - 1].name);
    },
    next() {
        this.go(this.menu[this.current + 1].name);
    },
    go(name) {
        if (this.active == name) return;
        if (this.$store.state.admin.radios == 2) {
          this.active = name;
        }else{
          this.validate(() => {
            this.active = name;
          });
        }
    },
    validate(callback) {
      return new Promise(resolve => {
        this.$refs.part.validate(function () {
          resolve(callback());
        })
      })
    }
  }
}