<template>
  <el-dialog title="快速查找" :visible.sync="visible" width="600px"
             class="media-dingwei-dialog" @closed="close"
             :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true">
    <form @submit.stop.prevent="search">
      <el-input v-model="input" placeholder="请粘贴资源详情地址" size="small" maxlength="128">
        <button slot="suffix" class="el-input__icon el-icon-search" type="submit"></button>
      </el-input>
    </form>

    <el-form size="small" label-width="80px">
      <el-form-item :label="data._type">{{data.title}}</el-form-item>
      <el-form-item label="状态">{{data._status}}</el-form-item>
      <el-form-item :label="author">{{data.author}}</el-form-item>
      <el-form-item label="发布时间">
        <a href="/staff" target="_blank" title="发布者认证主体" v-if="auditor"
           style="margin-right:10px">{{data.owner_nick}}</a>
        <span title="申请发布时间">{{data._pubdate}}</span>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-tag size="mini" v-show="data.id">{{data._audit_status}}</el-tag>
        {{data._audit_time}}<a href="/staff" target="_blank" v-if="auditor" style="margin-left:10px">{{data.auditor_name}}</a>
      </el-form-item>
      <el-form-item label="审核备注" v-if="auditor">
        <el-input v-if="data.audit_status===0" type="textarea" v-model.trim="data.audit_remark"
                  maxlength="255"></el-input>
        <template v-else>{{data.audit_remark}}</template>
      </el-form-item>
      <el-form-item label="驳回原因" v-if="data.audit_status===0 || data.audit_status===2">
        <el-input v-if="data.audit_status===0" type="textarea" v-model.trim="data.refuse_reason"
                  maxlength="255"></el-input>
        <template v-else>{{data.refuse_reason}}</template>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <template v-if="data.id">
        <el-button-group>
          <el-button @click="preview" size="small">预 览</el-button>
          <el-button @click="discuss" size="small">评 论</el-button>
          <el-button @click="del" size="small">删 除</el-button>
          <el-button type="primary" @click="edit" size="small" v-if="!auditor">编 辑</el-button>
        </el-button-group>
        <el-button-group v-if="auditor">
          <el-button type="primary" @click="online" size="small" title="发布此版本">上 架</el-button>
          <el-button type="primary" @click="offline" size="small" title="下架原作品">下 架</el-button>
        </el-button-group>
        <el-button-group size="small" v-if="auditor && data.audit_status===0">
          <el-button type="primary" @click="audit(1)" size="small" title="将审核状态变更为：已终审">通 过</el-button>
          <el-button type="primary" @click="audit(0)" size="small" title="下架原作品">驳 回</el-button>
        </el-button-group>
      </template>
      <el-button @click="visible = false" size="small" v-else>关 闭</el-button>
    </div>

    <reply v-if="reply.visible" :input="reply"></reply>
  </el-dialog>
</template>

<script>
  import reply from './reply';

  export default {
    components: {reply},
    props: ['visible', 'auditor'],
    data() {
      return {
        input: '',
        author: '作者',
        data: {
          id: 0,
          _type: '标题',
          title: '',
          _status: '',
          owner_name: '',
          owner_nick: '',
          _pubdate: '',
          publisher: '',
          author: '',
          _audit_status: '未知',
          auditor_name: '',
          audit_status: -1
        },
        reply: {visible: false, topicId: null}
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', 0);
      },
      search() {
        let alias = '';
        let input = this.input;

        if (/^[a-zA-Z0-9]+$/.test(input)) {
          alias = input;
        } else {
          let match = input.match(/media\/detail\/([a-zA-Z0-9]+)/);

          if (match) {
            alias = match[1];
          }
        }

        alias = this.$utils.decodeId(alias);
        if (!alias) {
          return this.data.id = 0;
        }

        API.media.find(alias.id, alias.type.substr(6)).then(res => {
          switch (res.type) {
            case 'expo':
              this.author = '主办单位';
              break;
            default:
              this.author = '作者';
              break;
          }
          this.data = res;
        });
      },
      audit(res) {
        let data = this.data;

        if (!res && data.refuse_reason.length < 5) {
          return this.$message.error('驳回原因不低于5个字');
        }

        API.media.audit.save({
          id: data.audit_id,
          type: data.type,
          result: res,
          remark: data.audit_remark,
          reason: data.refuse_reason
        }).then(this.search);
      },
      del() {
        let id, item = this.data;
        this.$confirm('操作不可恢复，请谨慎操作删除选项', '提示', {
          confirmButtonText: '此版本',
          cancelButtonText: '原作品',
          type: 'warning',
          distinguishCancelAndClose: true
        }).then(() => {
          id = item.target_id || item.id;
        }).catch((e) => {
          if (e == 'cancel') {
            id = item.prev_id || item.id;
          }
        }).finally(() => {
          id && API.media.audit.delete({id: id, type: item.type}).then(this.close);
        });
      },
      online() {
        let item = this.data;
        this.$confirm('确定上架此版本，并覆盖最新编辑数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return API.media.audit.online(item.prev_id || item.id, item.target_id || item.id, item.type);
        }).then(this.search);
      },
      offline() {
        let item = this.data;
        this.$confirm('确定下架【原作品】吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return API.media.audit.offline(item.prev_id || item.id, item.type);
        }).then(this.search);
      },
      discuss() {
        let item = this.data;
        Object.assign(this.reply, {visible: true, id: item.prev_id || item.id, topic_id: item.topic_id});
      },
      preview() {
        API.media.preview(this.data);
      },
      edit() {
        this.close();
        API.media.edit(this.data, this);
      }
    }
  }
</script>
