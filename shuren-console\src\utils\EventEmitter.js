export default function EventEmitter(scope) {
  let events = {};
  let bind = function (name, fn, once) {
    if (!events[name]) events[name] = [];
    events[name].push({fn, once: once ? 1 : 0});
  };

  let emitter = {
    on(name, fn) {
      bind(name, fn, 0);
    },
    once(name, fn) {
      bind(name, fn, 1);
    },
    off(name, fn) {
      let evs = events[name];
      if (evs) for (let i = evs.length - 1, ev; i > -1; i--) {
        ev = evs[i];
        if (!fn || ev.fn === fn) evs.splice(i, 1);
      }
    },
    emit(name, ...args) {
      let evs = events[name];
      if (!evs) return Promise.resolve();

      function dispatch(i) {
        let e = evs[i];

        if (!e) return Promise.resolve();
        if (e.once) {
          evs.splice(i, 1);
        } else {
          i++;
        }

        try {
          return Promise.resolve(e.fn(...args)).then(() => dispatch(i));
        } catch (err) {
          return Promise.reject(err)
        }
      }

      return dispatch(0);
    }
  };

  let props = {};
  for (let ev in emitter) {
    props[ev] = {
      value: emitter[ev]
    }
  }

  Object.defineProperties(scope, props);

  props = null;
  emitter = null;

  return scope;
}