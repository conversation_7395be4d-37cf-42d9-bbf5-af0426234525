<template>
  <div class="media-channel-panel">
    <el-input size="small" readonly :value="label" placeholder="请在下列面板中选择" style="margin-bottom:20px"></el-input>
    <el-cascader-panel
        v-model="model"
        :size="size"
        :placeholder="placeholder"
        :options="list"
        :props="{ expandTrigger: 'hover', checkStrictly: checkStrictly, label: 'name', value: 'id' }"
        @change="onChange"></el-cascader-panel>
  </div>
</template>

<script>
export default {
  name: 'ElChannel',
  props: {
    value: {},
    size: {
      default: 'small'
    },
    placeholder: {
      default: '请选择'
    },
    checkStrictly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      label: '',
      model: []
    }
  },
  computed: {
    kvobj() {
      return {}
    }
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        this.reset(this.list);
      }
    }
  },
  created() {
    this.$api.media.channel.list({owner: this.$store.state.admin.id}).then(list => {
      console.log(list);
      this.list = list;
      this.reset(list);
      this.eachData(list);
    });
  },
  methods: {
    reset(list) {
      if (list.length == 0) return;
      let path = this.value ? this.eachPath(list, []) : [];

      let label = [];
      let model = [];
      path.forEach(item => {
        label.push(item.name);
        model.push(item.id);
      });

      this.model = model;
      this.label = label.join(' / ');
    },
    onChange(arr) {
      let v = arr[arr.length - 1] || null;
      this.$emit('input', v);
      this.$emit('change', this.kvobj[v]);
    },
    eachPath(list, path) {
      for (let i = 0, len = list.length; i < len; i++) {
        if (list[i].id == this.value) {
          path.push(list[i]);
          return path;
        } else if (list[i].children) {
          let res = this.eachPath(list[i].children, path.concat(list[i]));
          if (res) return res;
        }
      }
    },
    eachData(list) {
      list.forEach(item => {
        this.kvobj[item.id] = {id: item.id, name: item.name};
        if (item.children) this.eachData(item.children);
      });
    }
  }
}
</script>