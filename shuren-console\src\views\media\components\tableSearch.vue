<template>
  <form class="table-toolbar" slot="toolbar" v-model="search">
    <div class="toolbar-header">
      <div class="toolbar-title"></div>
      <div class="toolbar-actions">
        <el-date-picker
            title="发布时间"
            v-model="pubdate"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            align="right"
            size="mini"
            @change="onPubdate">
        </el-date-picker>
        <el-input class="search-val" size="mini" style="width: 160px" v-model="search.kw" placeholder="请输入关键字"></el-input>
        <media-type v-model="search.type" style="width: 100px"></media-type>
        <el-button native-type="submit" icon="el-icon-search" size="mini" circle></el-button>
        <el-button type="primary" size="mini" icon="el-icon-edit" @click="onAdd()" circle></el-button>
      </div>
    </div>
  </form>
</template>

<script>

  function getTimeRange(time){
    let start = new Date(), end = new Date();
    start.setHours(0);
    start.setMinutes(0);
    start.setSeconds(0);
    start.setTime(start.getTime() - time * 86400000);
    end.setHours(23);
    end.setMinutes(59);
    end.setSeconds(59);
    return [start, end];
  }

  export default {
    props: ['value'],
    data(){
      return {
        search: {kw: '', pubdate: null, type: ''},
        pubdate: null,
        pickerOptions: {
          shortcuts: [{
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', getTimeRange(0));
            }
          }, {
            text: '昨天',
            onClick(picker) {
              let start = new Date(), end = new Date();

              start.setHours(0);
              start.setMinutes(0);
              start.setSeconds(0);
              start.setTime(start.getTime() - 86400000);

              end.setHours(23);
              end.setMinutes(59);
              end.setSeconds(59);
              end.setTime(end.getTime() - 86400000);

              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三天',
            onClick(picker) {
              picker.$emit('pick', getTimeRange(3));
            }
          }, {
            text: '最近一周',
            onClick(picker) {
              picker.$emit('pick', getTimeRange(7));
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              picker.$emit('pick', getTimeRange(30));
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              picker.$emit('pick', getTimeRange(90));
            }
          }]
        },
      }
    },

    methods: {
      onPubdate(v){
        this.search.pubdate = parseInt(v[0] / 1000) + '_' + parseInt(v[1] / 1000);
      },
    },
    created(){

    }
  }
</script>
