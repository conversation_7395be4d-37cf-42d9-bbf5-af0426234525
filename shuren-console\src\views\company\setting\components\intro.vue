<template>
  <div class="edit-intro">
    <div class="edit-panel-title">文字介绍</div>
    <div class="edit-panel-desc">组织详细情况介绍，该信息展示在大屏端【组织架构】中</div>
    <div class="edit-panel-body">
      <el-input v-model.trim="model.intro" type="textarea" :rows="15" maxlength="500" placeholder="请输入" show-word-limit></el-input>
      <div class="edit-panel-action">
        <el-button type="primary" @click="doSubmit">保 存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    model() {
      return this.$parent.model;
    }
  },
  methods: {
    doSubmit() {
      let {id, intro} = this.model;

      this.$api.post('/v1/company.info.update?action=intro', {
        id: id,
        intro: intro
      });
    }
  }
}
</script>