<template>
  <div class="el-input-mobile">
    <el-input v-model="mobile" :placeholder="placeholder" maxlength="11" :disabled="disabled" @change="mobileChanged">
      <template slot="prefix">
        <span class="el-input__icon">{{areaCode}}</span>
        <el-select v-model="areaCode" @change="areaChanged">
          <el-option
              v-for="(item, key) in options"
              :key="key"
              :label="item.name + item.prefix"
              :value="item.prefix">
          </el-option>
        </el-select>
      </template>
    </el-input>
  </div>
</template>

<script>
  export default {
    props: {
      mobile: {
        default() {
          return ''
        }
      },
      areaCode: {
        default() {
          return '+86'
        }
      },
      placeholder: {
        default() {
          return '请输入手机号码'
        }
      },
      disabled: {
        default() {
          return false;
        }
      }
    },
    computed: {
      options() {
        return this.$utils.mobielArea;
      }
    },
    created() {
      if (!this.areaCode) this.areaChanged('+86');
    },
    methods: {
      mobileChanged(v) {
        this.$emit('update:mobile', v);
        this.$emit('update:areaCode', this.areaCode);
      },
      areaChanged(v) {
        this.$emit('update:areaCode', v);
      }
    }
  }
</script>

<style lang="scss">
  .el-input-mobile {

    .el-input__icon {
      margin-left: 10px;
    }

    > .el-input > input {
      padding-left: 56px;
    }

    .el-select {
      z-index: 2;
      opacity: 0;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
    }
  }
</style>