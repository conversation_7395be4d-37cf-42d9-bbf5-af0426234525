<template>
  <div>
    <mytable ref="table" :list="list" :api="reload">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <el-button type="primary" size="mini" @click="onEdit()">添加</el-button>
          </div>
        </div>
      </div>

      <el-table-column label="部门名称" prop="name" width="320px"></el-table-column>
      <el-table-column label="部门编号" prop="id" align="center"></el-table-column>
      <el-table-column label="部门编码" prop="code" align="center"></el-table-column>
      <el-table-column label="部门人数" prop="people" align="center"></el-table-column>
      <el-table-column label="部门主管" prop="leader_name" align="center"></el-table-column>
      <el-table-column label="办公电话" prop="phone" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="created" align="center" :formatter="fCreate"></el-table-column>
      <el-table-column label="操作" align="center" width="90px">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDel(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <edit-dialog v-if="editing" @closed="editing=false" :value="model" :channels="list" @change="refresh"></edit-dialog>
  </div>
</template>

<script>
import EditDialog from './components/edit-dialog';

export default {
  name: 'DepartmentPage',
  components: {EditDialog},
  data() {
    return {
      list: [],
      editing: false,
      model: null
    }
  },
  methods: {
    reload() {
      return this.$api.department.list(this.$store.state.admin.company_id);
    },
    refresh() {
      this.$refs.table.refresh();
    },
    fCreate(row, column, val, index) {
      return this.$utils.Date(val).format('YYYY-MM-DD');
    },
    onEdit(row) {
      this.model = row;
      this.editing = true;
    },
    onDel(row) {
      this.$confirm('操作不可恢复，确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return this.$api.department.delete(row.id);
      }).then(this.refresh);
    }
  }
}
</script>