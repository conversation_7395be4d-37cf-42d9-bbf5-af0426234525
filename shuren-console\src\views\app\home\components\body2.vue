<template>
  <div class="home-editor-body">
    <editor-view v-for="model in list" :model="model"></editor-view>
    <!--<div class="body2PosBox" v-show="this.$store.state.admin.isSpecial && !$store.state.admin.pid">
       <div class="body2Backround" onclick="goDetail">
        <vue-seamless-scroll :data="textList" :class-option="optionHover" class="bodySeamless-warp">
          <p v-for="(v, i) in textList" :key="i" class="body2TopText">
            <span class="nameText">{{ v.title }}</span>
            <span class="timeText">{{ v.alias }}</span>
          </p>
        </vue-seamless-scroll>
      </div> 
      <div class="body2Bot">资讯汇聚</div>
    </div>-->
  </div>
</template>

<script>
import Base from "./body";
import request from "@/api/modules/request.js"
import vueSeamlessScroll from "vue-seamless-scroll";

export default {
  extends: Base,
  data() {
    return {
      status: false,
      list: [],
      textList: [],
    };
  },
  components: { vueSeamlessScroll },
  computed: {
    optionHover() {
      return {
        step: 0.4, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  created() {
    // this.list =
    //   this.model.theme == 2
    //     ? this.model.body
    //     : 
    if (this.$store.state.admin.isSpecial) {
      console.log(1);
      if (this.$store.state.admin.pid) {
        
        this.list = 
        this.model.theme == 2 ? this.model.body :
        [
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 0,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "1",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 458,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "2",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 914,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "3",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 1372,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "4",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 0,
              y: 296,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "5",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 458,
              y: 296,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                // opacity: 0.2,
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "6",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 914,
              y: 296,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                // opacity: 0.2,
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "7",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
          ];
      } else {

        this.list = 
        this.model.theme == 2 ? this.model.body :
          [
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 0,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "1",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 458,
              y: 0,
              r: 0,
              loop: 1,
              address: "",
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "2",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 914,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "3",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 1372,
              y: 0,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "4",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 0,
              y: 296,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "5",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
            {
              name: "rect",
              width: 428,
              height: 266,
              x: 458,
              y: 296,
              r: 0,
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                enabled: 1,
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.2)",
                // opacity: 0.2,
                image: "",
                size: "cover",
              },
              children: [
                {
                  name: "label",
                  value: "6",
                  width: 428,
                  height: 50,
                  x: 0,
                  y: 216,
                  r: 0,
                  font: {
                    size: 20,
                    color: "#fff",
                    weight: 500,
                    height: 50,
                    align: "center",
                  },
                  shadow: { x: 0, y: 0, b: 20, color: "#000000" },
                  border: {
                    width: 0,
                    style: "none",
                    color: "none",
                    radius: 10,
                  },
                  background: {
                    color: "rgba(0,0,0,0.4)",
                    image: "",
                    size: "cover",
                  },
                },
              ],
            },
          ];
      }
    } else {
    //   this.list = this.model.theme == 2 ? this.model.body :
    //     [
    //       {
    //         name: "video",
    //         width: 886,
    //         height: 562,
    //         x: 0,
    //         y: 0,
    //         r: 0,
    //         loop: 1,
    //         address: "",
    //         shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //         border: {
    //           enabled: 1,
    //           width: 0,
    //           style: "none",
    //           color: "none",
    //           radius: 10,
    //         },
    //         background: {
    //           color: "rgba(0,0,0,0.2)",
    //           image: "",
    //           size: "cover",
    //         },
    //       },
    //       {
    //         name: "rect",
    //         width: 428,
    //         height: 266,
    //         x: 914,
    //         y: 0,
    //         r: 0,
    //         shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //         border: {
    //           enabled: 1,
    //           width: 0,
    //           style: "none",
    //           color: "none",
    //           radius: 10,
    //         },
    //         background: {
    //           color: "rgba(0,0,0,0.2)",
    //           image: "",
    //           size: "cover",
    //         },
    //         children: [
    //           {
    //             name: "label",
    //             value: "1",
    //             width: 428,
    //             height: 50,
    //             x: 0,
    //             y: 216,
    //             r: 0,
    //             font: {
    //               size: 20,
    //               color: "#fff",
    //               weight: 500,
    //               height: 50,
    //               align: "center",
    //             },
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.4)",
    //               image: "",
    //               size: "cover",
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         name: "rect",
    //         width: 428,
    //         height: 266,
    //         x: 1372,
    //         y: 0,
    //         r: 0,
    //         shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //         border: {
    //           enabled: 1,
    //           width: 0,
    //           style: "none",
    //           color: "none",
    //           radius: 10,
    //         },
    //         background: {
    //           color: "rgba(0,0,0,0.2)",
    //           image: "",
    //           size: "cover",
    //         },
    //         children: [
    //           {
    //             name: "label",
    //             value: "2",
    //             width: 428,
    //             height: 50,
    //             x: 0,
    //             y: 216,
    //             r: 0,
    //             font: {
    //               size: 20,
    //               color: "#fff",
    //               weight: 500,
    //               height: 50,
    //               align: "center",
    //             },
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.4)",
    //               image: "",
    //               size: "cover",
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         name: "rect",
    //         width: 428,
    //         height: 266,
    //         x: 914,
    //         y: 296,
    //         r: 0,
    //         shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //         border: {
    //           enabled: 1,
    //           width: 0,
    //           style: "none",
    //           color: "none",
    //           radius: 10,
    //         },
    //         background: {
    //           color: "rgba(0,0,0,0.2)",
    //           image: "",
    //           size: "cover",
    //         },
    //         children: [
    //           {
    //             name: "label",
    //             value: "3",
    //             width: 428,
    //             height: 50,
    //             x: 0,
    //             y: 216,
    //             r: 0,
    //             font: {
    //               size: 20,
    //               color: "#fff",
    //               weight: 500,
    //               height: 50,
    //               align: "center",
    //             },
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.4)",
    //               image: "",
    //               size: "cover",
    //             },
    //           },
    //         ],
    //       },
    //       {
    //         name: "rect",
    //         width: 428,
    //         height: 266,
    //         x: 1372,
    //         y: 296,
    //         r: 0,
    //         shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //         border: {
    //           enabled: 1,
    //           width: 0,
    //           style: "none",
    //           color: "none",
    //           radius: 10,
    //         },
    //         background: {
    //           color: "rgba(0,0,0,0.2)",
    //           image: "",
    //           size: "cover",
    //         },
    //         children: [
    //           {
    //             name: "label",
    //             value: "4",
    //             width: 428,
    //             height: 50,
    //             x: 0,
    //             y: 216,
    //             r: 0,
    //             font: {
    //               size: 20,
    //               color: "#fff",
    //               weight: 500,
    //               height: 50,
    //               align: "center",
    //             },
    //             shadow: { x: 0, y: 0, b: 20, color: "#000000" },
    //             border: {
    //               width: 0,
    //               style: "none",
    //               color: "none",
    //               radius: 10,
    //             },
    //             background: {
    //               color: "rgba(0,0,0,0.4)",
    //               image: "",
    //               size: "cover",
    //             },
    //           },
    //         ],
    //       },
    //     ];
    // }
    [
        {
          name: "video",
          width: 886,
          height: 562,
          x: 0,
          y: 0,
          r: 0,
          loop: 1,
          address: "",
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            image: "",
            size: "cover",
          },
        },
        {
          name: "rect",
          width: 428,
          height: 266,
          x: 914,
          y: 0,
          r: 0,
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            image: "",
            size: "cover",
          },
          children: [
            {
              name: "label",
              value: "1",
              width: 428,
              height: 50,
              x: 0,
              y: 216,
              r: 0,
              font: {
                size: 20,
                color: "#fff",
                weight: 500,
                height: 50,
                align: "center",
              },
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.4)",
                image: "",
                size: "cover",
              },
            },
          ],
        },
        {
          name: "rect",
          width: 428,
          height: 266,
          x: 1372,
          y: 0,
          r: 0,
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            image: "",
            size: "cover",
          },
          children: [
            {
              name: "label",
              value: "2",
              width: 428,
              height: 50,
              x: 0,
              y: 216,
              r: 0,
              font: {
                size: 20,
                color: "#fff",
                weight: 500,
                height: 50,
                align: "center",
              },
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.4)",
                image: "",
                size: "cover",
              },
            },
          ],
        },
        {
          name: "rect",
          width: 428,
          height: 266,
          x: 914,
          y: 296,
          r: 0,
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            image: "",
            size: "cover",
          },
          children: [
            {
              name: "label",
              value: "3",
              width: 428,
              height: 50,
              x: 0,
              y: 216,
              r: 0,
              font: {
                size: 20,
                color: "#fff",
                weight: 500,
                height: 50,
                align: "center",
              },
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.4)",
                image: "",
                size: "cover",
              },
            },
          ],
        },
        {
          name: "rect",
          width: 428,
          height: 266,
          x: 1372,
          y: 296,
          r: 0,
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            opacity: 0.2,
            image: "http://cdn.dangjian.nextv.show/app/icon/9.jpg",
            size: "cover",
          },
          children: [
            {
              name: "label",
              value: "4",
              width: 428,
              height: 50,
              x: 0,
              y: 216,
              r: 0,
              font: {
                size: 20,
                color: "#fff",
                weight: 500,
                height: 50,
                align: "center",
              },
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.4)",
                image: "",
                size: "cover",
              },
            },
          ],
        },
        {
          name: "rect",
          width: 428,
          height: 266,
          x: 1372,
          y: 296,
          r: 0,
          shadow: { x: 0, y: 0, b: 20, color: "#000000" },
          border: {
            enabled: 1,
            width: 0,
            style: "none",
            color: "none",
            radius: 10,
          },
          background: {
            color: "rgba(0,0,0,0.2)",
            // opacity: 0.2,
            image: "",
            size: "cover",
          },
          children: [
            {
              name: "label",
              value: "4",
              width: 428,
              height: 50,
              x: 0,
              y: 216,
              r: 0,
              font: {
                size: 20,
                color: "#fff",
                weight: 500,
                height: 50,
                align: "center",
              },
              shadow: { x: 0, y: 0, b: 20, color: "#000000" },
              border: {
                width: 0,
                style: "none",
                color: "none",
                radius: 10,
              },
              background: {
                color: "rgba(0,0,0,0.4)",
                image: "",
                size: "cover",
              },
            },
          ],
        },
    ]
    }

    console.log(this.list);

    // if (!this.$store.state.admin.pid) {
    //   request.get('/v1/media.item.subList', { creator: this.$store.state.admin.id }).then((res) => {
    //     console.log(res);
    //     this.textList = res
    //   }).catch(err => {
    //     console.log(err);
    //   })
    // } else {
    //   request.get('/v1/media.item.pList').then((res) => {
    //     console.log(res);
    //     this.textList = res
    //   }).catch(err => {
    //     console.log(err);
    //   })
    // }

  },
  filters: {
    typeFormat(v) {
      switch (v) {
        case "news": {
          return "图文素材"
        }
        case "video": {
          return "视频素材"
        }
        case "audio": {
          return "音频素材"
        }
        case "motto": {
          return "金句"
        }
        case "image": {
          return "PPT"
        }
        default: {
          return "其他类型"
        }
      }
    }
  }
};
</script>
<style lang="scss">
.home-editor-bod {
  position: relative;
}

.body2PosBox {
  width: 428px;
  height: 266px;
  position: fixed;
  top: 482px;
  right: 60px;
  border-radius: 10px;
}

.body2Top {
  width: 428px;
  height: 266px;
  border-radius: 10px;
  padding-top: 15px;
  padding-bottom: 20px;
  background-color: #f08733;
  overflow: auto;
}

.body2Img {
  width: 428px;
  height: 266px;
  opacity: 0.1;
  background: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  right: 0;
}

::-webkit-scrollbar {
  display: none;
}

.body2TopText {
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: space-around;
  line-height: 30px;
  z-index: 99;
  padding: 0 20px;
}

.body2NameText {
  color: #fdf451;
  width: 30%;
  height: 100%;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: left;
}

.body2TitleText {
  color: #f1f1f1;
  flex: 1;
  font-size: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: center;
}

.body2TimeText {
  color: white;
  width: 30%;
  height: 100%;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.body2Bot {
  width: 100%;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 10px 10px;
  font-size: 25px;
  color: white;
  text-align: center;
  line-height: 50px;
  z-index: 99;
}

.body2Backround {
  width: 428px;
  height: 266px;
  border-radius: 10px;
  padding-top: 20px;
  padding-bottom: 30px;
  // background-color: #f08733;
  overflow: auto;
  background: url(http://cdn.zn.nextv.show/img/20230614/35151357/962.png) no-repeat;
  background-size: 100% 100%;
}

.bodySeamless-warp {
  height: 266px;
  overflow: hidden;
}
</style>
