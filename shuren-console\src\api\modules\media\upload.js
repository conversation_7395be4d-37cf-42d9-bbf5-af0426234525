import request from "../request";

// 上传到本地服务器
function sysBucket(file, key, opt, progress) {
  let form = new FormData();
  form.append('key', key);
  form.append('file', file);
  form.append('name', file.name);

  if (opt.append) {
    for (let field in opt.append) {
      form.append(field, opt.append[field]);
    }
  }

  return request({
    url: opt.remote,
    method: 'POST',
    data: form,
    timeout: 10800000,
    responseType: 'json',
    onUploadProgress(e) {
      e.lengthComputable && progress(e)
    }
  });
}

// 上传到七牛云
function qiniuBucket(file, key, opt, progress) {
  let form = new FormData();
  form.append('file', file);
  form.append('token', opt.token);
  form.append('key', key);
  form.append('fname', file.name);

  if (opt.append) {
    for (let field in opt.append) {
      form.append('x:' + field, opt.append[field]);
    }
  }

  return request({
    url: opt.remote,
    method: 'POST',
    data: form,
    timeout: 10800000,
    responseType: 'json',
    onUploadProgress(e) {
      e.lengthComputable && progress(e)
    }
  });
}

// 上传到阿里云
function aliBucket(file, key, opt, progress) {
  let form = new FormData();

  form.append('name', file.name);
  if (opt.callback) form.append('callback', opt.callback);
  form.append('key', key);
  form.append('policy', opt.policy);
  form.append('OSSAccessKeyId', opt.accessid);
  form.append('success_action_status', 200);
  form.append('signature', opt.signature);
  form.append('file', file);

  if (append) {
    for (let field in append) {
      form.append('x:' + field, append[field]);
    }
  }

  return request({
    url: opt.remote,
    method: 'POST',
    data: form,
    timeout: 10800000,
    responseType: 'text',
    onUploadProgress(e) {
      e.lengthComputable && progress(e)
    }
  });
}

// 上传到华为云
function huaweiBucket(file, key, opt, progress) {
  let form = new FormData();

  form.append('file', file);
  form.append('key', key);
  form.append('AccessKeyId', opt.accessKey);
  form.append('policy', opt.policy);
  //form.append('token', opt.token);
  form.append('x-obs-acl', opt.acl);
  form.append('success_action_redirect', '');
  form.append('success_action_status', 200);

  return request({
    url: opt.remote,
    method: 'POST',
    data: form,
    timeout: 10800000,
    responseType: 'text',
    onUploadProgress(e) {
      e.lengthComputable && progress(e)
    }
  });
}

function doUpload(file, key, opt, progress) {
  switch (opt.type) {
    case 0:
      return sysBucket(file, key, opt, progress);
    case 1:
      return qiniuBucket(file, key, opt, progress);
    case 2:
      return aliBucket(file, key, opt, progress);
    case 3:
      return huaweiBucket(file, key, opt, progress);
    default:
      throw new Error('未指定资源服务器');
  }
}

function toFloat(v) {
  const s = v.toString().split('.');
  return s.length == 1 ? parseInt(v) : parseFloat(s[0] + '.' + s[1].substr(0, 2));
}

export default {
  config() {
    return request.get('/v1/media.upload.config');
  },
  request(body, files, progress) {
    // console.log(files);
    return request.post('/v1/media.upload.request', body).then(opt => {
      files = Array.isArray(files) ? files : [files];

      let p;
      let totalFile = files.length;
      let totalSize = 0;
      let loadedSize = 0;

      let upload = function (index) {
        if (index > totalFile) {
          return files[0].result;
        }

        let file = files[index - 1];

        if (file.result) {
          totalSize += file.size;
          loadedSize += file.size;
          p = toFloat(loadedSize / totalSize * 100);
          progress(p, loadedSize, totalSize);
          return upload(index + 1);
        }

        let ext = file.name.substr(file.name.lastIndexOf('.'));
        let key = opt.prefix + (ext == '.ts' ? file.name : Math.random().toString().substr(-3) + ext);
        let total = 0;
        let loaded = 0;

        return doUpload(file, key, opt, function (e) {
          total = totalSize + e.total;
          loaded = loadedSize + e.loaded;
          p = toFloat(loaded / total * 100);
          progress(p, loaded, total);
        }).then(res => {
          totalSize = total;
          loadedSize = loaded;
          file.result = res;
          return upload(index + 1);
        });
      }

      return upload(1);
    });
  }
}
