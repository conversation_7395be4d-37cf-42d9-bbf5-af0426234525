<template>
  <el-dropdown class="home-editor-tool" @command="onCommand">
    <div class="home-editor-tool-button">
      <i class="el-icon-s-tools"></i>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="bgimage">背景图片</el-dropdown-item>
      <el-dropdown-item command="bgcolor">背景颜色</el-dropdown-item>
      <el-dropdown-item command="clearimg">清除背景</el-dropdown-item>
      <el-dropdown-item command="grayscale">一键灰白</el-dropdown-item>
      <el-dropdown-item command="style-0" divided>风格一</el-dropdown-item>
      <el-dropdown-item command="style-1">风格二</el-dropdown-item>
      <el-dropdown-item command="style-2">风格三</el-dropdown-item>
      <el-dropdown-item command="submit" divided>保存数据</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import SelectImage from "@/views/media/image/select";
import SelectColor from "@/components/color/dialog";

export default {
  computed: {
    model() {
      // console.log(this.$parent.model);
      return this.$parent.model;
    },
  },
  methods: {
    onCommand(v) {
      switch (v) {
        case "bgimage":
          return this.onImage();
        case "bgcolor":
          return this.onColor();
        case "clearimg":
          this.model.bgimage = "";
          return;
        case "grayscale":
          this.model.grayscale = this.model.grayscale == 0 ? 100 : 0;
          return;
        case "style-0":
        case "style-1":
        case "style-2":
          this.$parent.theme = v.split("-")[1];
          return;
        case "submit":
          return this.onSubmit();
      }
    },
    onImage() {
      SelectImage(1, {
        accept: "image/jpeg,image/png",
        placeholder: "建议尺寸1920x1080像素，文件大小不超过1M",
        size: 1024,
      }).then((list) => {
        this.model.bgimage = list[0].url;
      });
    },
    onColor() {
      let { model } = this;
      SelectColor(model.bgcolor, "背景颜色").then((v) => {
        model.bgcolor = v;
      });
    },
    onSubmit() {
      this.model.theme = this.$parent.theme;
      console.log(this.model);
      this.$api.post("/v1/app.home.set", this.model);
    },
  },
};
</script>