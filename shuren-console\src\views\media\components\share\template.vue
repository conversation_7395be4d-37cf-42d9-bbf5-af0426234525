<template>
  <el-dialog
      title="分享"
      class="media-share-dialog"
      :visible.sync="visible"
      width="800px"
      @closed="closed">
    <div style="display:flex;align-items:center">
      <div class="share-img">
        <h4>作品二维码</h4>
        <a class="qrcode" :href="qrcode" :download="download">
          <template v-if="status==0">生成中</template>
          <template v-else-if="status==2">生成失败</template>
          <img :src="qrcode" v-else>
        </a>
      </div>
      <div class="share-code">
        <h4 class="relative">作品地址：
          <span class="share-copy" data-id="link">复制</span>
        </h4>
        <el-input :value="link" readonly="readonly" size="small"></el-input>
        <h4 class="relative">内嵌地址：
          <span class="share-copy" data-id="ifurl">复制</span>
        </h4>
        <el-input :value="ifurl" readonly="readonly" size="small"></el-input>
        <h4 class="relative">
          <span>网站嵌入：</span>
          <span class="share-copy" data-id="iframe">复制</span>
        </h4>
        <el-input :value="iframe" readonly="readonly" size="small"></el-input>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="preview">查 看</el-button>
      <el-button type="primary" @click="visible=false">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      id: 0,
      type: '',
      status: 0,
      qrcode: '',
      link: '',
      ifurl: ''
    }
  },
  computed: {
    download() {
      return this.type + '_' + this.id + '.png';
    },
    iframe() {
      return '<iframe src="' + this.ifurl + '" allowFullScreen="true" frameborder="0"></iframe>';
    }
  },
  created() {
    Promise.all([
      this.$api.media.share(this.id, this.type),
      this.$require('qrcode')
    ]).then(([res]) => {
      this.link = this.APP_URL + res.inner_link;
      this.ifurl = this.APP_URL + res.outer_link;
      this.drawQrcode(res.logo || this.$env.CDN_URL + '/sys/qrlogo.jpg');
    })
  },
  mounted() {
    this.visible = true;
    this.$require('clipboard').then(this.clipboard);
  },
  destroyed() {
    this.clip && this.clip.destroy()
  },
  methods: {
    closed() {
      this.$destroy();
      this.$el.remove();
    },
    drawFailed(e) {
      this.status = 2;
    },
    drawQrcode(logo) {
      let width = 720, logoWidth = 160, canvas = document.createElement('canvas');

      QRCode.toCanvas(canvas, this.link, {
        errorCorrectionLevel: 'H',
        width: width,
        height: width,
        margin: 4
      }, (err) => {
        if (err) return this.drawFailed(err);

        let ctx = canvas.getContext("2d")
            , img = document.createElement('img');

        img.crossOrigin = 'anonymous';

        img.onload = () => {
          let p = (width - logoWidth) / 2;
          ctx.drawImage(img, p, p, logoWidth, logoWidth);

          this.drawRoundedRect(ctx, p, p, logoWidth, logoWidth, 20);

          this.qrcode = canvas.toDataURL('image/png');
          this.status = 1;
        };

        img.src = logo;
      });
    },
    drawRoundedRect(ctx, x, y, width, height, r) {
      ctx.save();
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.arcTo(x + width, y, x + width, y + r, r);
      ctx.arcTo(x + width, y + height, x + width - r, y + height, r);
      ctx.arcTo(x, y + height, x, y + height - r, r);
      ctx.arcTo(x, y, x + r, y, r);
      ctx.lineWidth = 20;
      ctx.strokeStyle = "#fff";
      ctx.stroke();
      ctx.restore();
    },
    clipboard() {
      let scope = this;
      let clipboard = new ClipboardJS('.share-copy', {
        text(trigger) {
          return scope[trigger.getAttribute('data-id')];
        }
      });

      clipboard.on('success', (e) => {
        e.clearSelection();
        this.$notify.success('复制成功');
      });

      clipboard.on('error', (e) => {
        $this.$notify.error('复制失败');
      });

      this.clip = clipboard;
    },
    preview() {
      window.open(this.link);
    }
  }
}
</script>

<style lang="scss">
.media-share-dialog {
  .el-dialog__body {
    padding: 20px 56px 40px 56px;
  }

  .qrcode {
    width: 220px;
    height: 220px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #f1f1f1;
  }

  h4 {
    margin-top: 26px;
  }

  .share-code {
    flex: 1;
    margin-left: 50px;
  }

  .tip {
    color: #999;
    margin-top: 16px;
  }

  .share-copy {
    position: absolute;
    top: 8px;
    right: 0;
    color: #409eff;
    font-size: 12px;
    cursor: pointer;
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
  }
}
</style>
