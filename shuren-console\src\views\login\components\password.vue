<template>
  <div class="login-container">
    <div class="login-header">
      <div class="login-type">
        <label class="login-label active">密码登录</label>
        <label class="login-label" @click="$emit('change', 'captcha')">短信登录</label>
      </div>
      <div class="switch-type">
        <label class="switch-label">微信登录</label>
        <div class="qrcode" @click="$emit('change', 'weixin', agree)"></div>
      </div>
    </div>
    <el-form class="login-body" ref="form" :model="model" :rules="rules" @submit.native.prevent="submit">
      <el-form-item prop="username">
        <el-input v-model.trim="model.username" placeholder="账号" autofocus="true" autocomplete="off"
                  maxlength="32"></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="model.password" :type="showPwd ? 'text' : 'password'" placeholder="密码"
                  autocomplete="off" maxlength="128"></el-input>
        <div class="toggle-pwd">
          <svg slot="suffix" class="icon" @click="showPwd=!showPwd">
            <use :xlink:href="`#${showPwd ? 'show' : 'hide'}-pwd`"></use>
          </svg>
        </div>
      </el-form-item>
      <div class="autologin">
        <el-checkbox v-model="model.keep_login" :true-label="3" :false-label="0">三天内自动登录</el-checkbox>
      </div>
      <el-button class="login-button" type="primary" native-type="submit" :disabled="!agree || loading">登 录</el-button>
    </el-form>
    <div class="login-footer">
      <agree v-model="agree"></agree>
      <div class="float-right">
        <a href="javascript:" class="forget">忘记密码</a>
        <router-link :to="register">免费注册</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import Agree from './agree';
export default {
  components: {Agree},
  props: ['loading'],
  data() {
    return {
      showPwd: false,
      agree: !!localStorage.getItem('AgreeLogin'),
      model: {
        by: 'password',
        username: '',
        password: '',
        keep_login: 3
      },
      rules: {
        username: {required: true, message: '请输入账号', trigger: 'change'},
        password: [
          {required: true, message: '请输入密码', trigger: 'change'},
          {min: 6, max: 32, message: '长度在 6 到 32 个字符', trigger: 'change'}
        ]
      }
    }
  },
  computed: {
    register() {
      return {name: 'register', query: {redirect: this.$route.query.redirect}}
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        let model = Object.assign({}, this.model, {password: this.$utils.md5(this.model.password)});

        this.$emit('submit', model);
      });
    },
    onWechat() {

    }
  }
}
</script>