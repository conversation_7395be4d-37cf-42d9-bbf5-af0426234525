import request from '../request'
// import userRequest from '../userRequest'
import channel from './channel'
import utils from '@/utils'
import upload from './upload'

let headReq = {
  task: 0,
  queue: [],
  execute() {
    let that = this,
      queue = that.queue

    if (that.task == 5 || queue.length == 0) {
      return
    }

    let params = queue.shift()
    let res = sessionStorage.getItem(params.url)
    if (res) {
      return params.resolve(JSON.parse(res))
    }

    that.task++

    request
      .head(params.url)
      .then(function (res) {
        sessionStorage.setItem(
          params.url,
          JSON.stringify({
            'content-length': res['content-length'],
            'content-type': res['content-type'],
          })
        )
        params.resolve(res)
      })
      .catch(function (e) {
        params.reject(e.message || e)
      })
      .finally(function (e) {
        that.task--
        that.execute()
      })
  },
  push(url, resolve, reject) {
    this.queue.push({
      url: url,
      resolve: resolve,
      reject: reject,
    })

    this.execute()
  },
}

const StatusList = [
  { label: '上传中', value: 'uploading' },
  { label: '转码中', value: 'transcoding' },
  { label: '编辑中', value: 'editing' },
  { label: '审核中', value: 'auditing' },
  { label: '被驳回', value: 'rejected' },
  { label: '已发布', value: 'online' },
  { label: '已下架', value: 'offline' },
  { label: '已过期', value: 'expired' },
]

const StatusObject = StatusList.reduce(
  (obj, item) => {
    obj[item.value] = item.label
    return obj
  },
  { deleted: '已删除' }
)

const TypeList = [
  { label: '图文', value: 'news' },
  { label: '视频', value: 'video' },
  { label: '音频', value: 'audio' },
  { label: '金句', value: 'motto' },
  { label: 'PPT', value: 'image' },
]

const TypeObject = TypeList.reduce((obj, item) => {
  obj[item.value] = item.label
  return obj
}, {})

export default {
  channel: channel,
  upload: upload,
  head(url) {
    return new Promise(function (resolve, reject) {
      headReq.push(url, resolve, reject)
    })
  },
  save(params, newest) {
    return request.post('/v1/media.item.save', params).then((res) => {
      return newest ? this.get(res.id, res.type, res.version) : res
    })
  },
  get(id, type, version) {
    return request.get('/v1/media.item.get?id=' + id + '&type=' + type + '&version=' + (version || ''))
  },
  publish(id, type, version) {
    return request.post('/v1/media.item.publish', { id: id, type: type, version: version })
  },
  offline(id, type) {
    return request.post('/v1/media.item.offline', { id: id, type: type })
  },
  list(query) {
    return request.get('/v1/media.item.query', query)
  },
  //层级管理list
  tierList(query) {
    return request.get('/v1/user.account.query', query)
  },

  auditList(data) {
    return request.post('/v1/user.account.verify', data)
  },
  //发布位置选择用户
  subUsersList() {
    return request.get('/v1/user.account.subUsers')
  },
  typeList: TypeList,
  type: TypeObject,
  statusList: StatusList,
  status: StatusObject,
  share(id, type, version) {
    return request.get('/v1/media.item.share', { id: id, type: type, version: version })
  },
  preview(id, type, version, prefix) {
    return request.post('/v1/media.item.preview', { id: id, type: type, version: version, prefix: prefix }).then((res) => {
      if (res.url) window.open(res.url)
    })
  },
  delete(id, type, version) {
    return request({ url: '/v1/media.item.delete', method: 'post', data: { id: id, type: type, version: version } })
  },
  search(q) {
    return request.get('/v1/media.item.search', q)
  },

  version(itemId, type) {
    return request.get('media.item.version?id=' + itemId + '&type=' + type).then((list) => {
      return (
        list.forEach((item) => {
          item._created = utils.Date(item.created).toString('YYYY-MM-DD HH:mm')
        }),
        list
      )
    })
  },
  // 创建上传的轮播素材
  createSwipe(params) {
    return request.post('/v1/home.carousel.create', params)
  },
}
