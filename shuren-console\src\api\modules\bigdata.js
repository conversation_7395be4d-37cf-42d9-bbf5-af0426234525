// import request from './request'
// export default {
//   list(companyId) {
//     return request.get('/v1/region.info.query?company_id=' + companyId)
//   },
//   create(data) {
//     return request.post('/v1/company.dept.create', data)
//   },
//   update(data) {
//     return request.post('/v1/company.dept.update', data)
//   },
//   delete(id) {
//     return request.post('/v1/company.dept.delete', { id: id })
//   },
// }
