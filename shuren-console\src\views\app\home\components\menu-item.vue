<template>
  <div class="home-editor-menu-item" v-if="$store.state.admin.pid">
    <div class="home-editor-menu-icon" :style="`background-color: ${model.fill}`" @dblclick.stop.prevent="onColor"
         title="双击更改背景颜色">
      <img :src="model.icon" @dblclick.stop.prevent="onIcon" title="双击上传图标">
      <div v-show="model.accessFlag" class="home-editor-link" title="点击更改链接地址" @click.stop.prevent="onLink"><i
           class="el-icon-link"></i></div>
    </div>
    <text-label class="home-editor-menu-label" v-model="model.title" maxlength="8" title="点击修改文字"></text-label>
  </div>
  <div class="home-editor-menu-item" v-else>
    <div class="home-editor-menu-icon" :style="`background-color: ${model.fill}`" @dblclick.stop.prevent="onColor"
         title="双击更改背景颜色">
      <img :src="model.icon" @dblclick.stop.prevent="onIcon" title="双击上传图标">
      <div class="home-editor-link" title="点击更改链接地址" @click.stop.prevent="onLink"><i class="el-icon-link"></i></div>
    </div>
    <text-label class="home-editor-menu-label" v-model="model.title" maxlength="8" title="点击修改文字"></text-label>
  </div>
</template>

<script>
import TextLabel from './text';
import SelectLink from '@/components/link/dialog';
import SelectImage from '@/views/media/image/select';
import SelectColor from '@/components/color/dialog';

export default {
  components: { TextLabel },
  props: ['model'],
  methods: {
    onIcon() {
      if (this.$store.state.admin.pid && this.model.accessFlag) {
        SelectImage(1, { accept: 'image/jpeg,image/png', placeholder: '.png格式，建议尺寸300x300像素，文件大小不超过300KB' }).then(list => {
          this.model.icon = list[0].url
        });
      }
      if (!this.$store.state.admin.pid) {
        SelectImage(1, { accept: 'image/jpeg,image/png', placeholder: '.png格式，建议尺寸300x300像素，文件大小不超过300KB' }).then(list => {
          this.model.icon = list[0].url
        });
      }
    },
    onColor() {
      if (this.$store.state.admin.pid && this.model.accessFlag) {
        let { model } = this;
        SelectColor(model.fill, '背景颜色').then(v => {
          model.fill = v;
        });
      }
      if (!this.$store.state.admin.pid) {
        let { model } = this;
        SelectColor(model.fill, '背景颜色').then(v => {
          model.fill = v;
        });
      }
    },
    onLink() {
      if (this.$store.state.admin.pid && this.model.accessFlag) {

        let { model } = this, { name, params, query } = model;
        SelectLink({ name, params, query }).then(to => {
          model.name = to.name;
          model.params = to.params;
          model.query = to.query;
        });
      }
      if (!this.$store.state.admin.pid) {
        let { model } = this, { name, params, query } = model;
        SelectLink({ name, params, query }).then(to => {
          model.name = to.name;
          model.params = to.params;
          model.query = to.query;
        });
      }
    }
  },
  mounted() {
  }
}
</script>