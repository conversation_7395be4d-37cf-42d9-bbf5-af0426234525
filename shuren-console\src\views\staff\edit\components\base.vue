<template>
  <div class="edit-panel-content">
    <div class="edit-panel-title">基本信息</div>
    <el-form class="edit-panel-body" ref="form" :model="model" :rules="rules" label-width="90px" label-suffix=":" size="small" style="padding-right:40px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓　　名" prop="name">
            <el-input v-model.trim="model.name" maxlength="32" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序序号" prop="sequence">
            <el-input v-model.number="model.sequence" placeholder="数字越小越靠前，范围：1-9999" maxlength="8"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="性　　别" prop="sex">
            <el-radio-group v-model="model.sex" size="small">
              <el-radio :label="1" border>　男　</el-radio>
              <el-radio :label="2" border>　女　</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="民　　族">
            <dictionary v-model="model.nation" type="nation"></dictionary>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="状　　态" prop="status">
            <el-radio-group v-model="model.status" size="small">
              <el-radio :label="1" border>履职中</el-radio>
              <el-radio :label="2" border>已离开</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期">
            <el-date-picker v-model="model.birthday" type="date" placeholder="选择日期" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" style="width:100%"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="用户分组">
        <select-staff-tag v-model="model.tag" multiple showRefresh showCreate></select-staff-tag>
      </el-form-item>
      <el-form-item label="毕业院校">
        <el-school v-model="model.school_name"></el-school>
      </el-form-item>
      <el-form-item label="最高学历">
        <dictionary v-model="model.education" type="education"></dictionary>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="参加工作">
            <el-date-picker v-model="model.join_work" type="month" placeholder="选择日期" format="yyyy年MM月" value-format="yyyy-MM-01"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职业职称">
            <el-input v-model.trim="model.position_name" placeholder="如：高中数学老师"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="edit-panel-action">
      <el-button type="primary" @click="onSubmit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import Dictionary from '@/components/dictionary/select';
import ElSchool from '@/components/dictionary/school';
import SelectStaffTag from "@/views/staff/tag/components/select";

export default {
  components: {SelectStaffTag, Dictionary, ElSchool},
  data() {
    return {
      rules: {
        name: {required: true, trigger: 'blur', message: '必填项'},
        sex: {
          required: true, trigger: 'blur', message: '必填项', validator(rule, value, callback) {
            if (!value) callback('必选项');
            else callback();
          }
        },
        status: {
          required: true, trigger: 'blur', message: '必填项', validator(rule, value, callback) {
            if (!value) callback('必选项');
            else callback();
          }
        },
        sequence: [
          {required: true, trigger: 'blur', message: '必填项'},
          {type: 'integer', trigger: 'blur', min: 1, max: 9999, message: '只允许填写1-9999之间的数字'}
        ]
      }
    }
  },
  computed: {
    model() {
      return this.$parent.model;
    }
  },
  methods: {
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    onSubmit() {
      this.validate(this.doSubmit);
    },
    doSubmit() {
      let {isAdd} = this.$parent;
      let body = {};
      let field = 'id,company_id,tag,headimg,name,sex,sequence,status,birthday,nation,school_name,education,join_work,position_name';
      field.split(',').forEach(key => {
        body[key] = this.model[key];
      });

      this.$parent.submit('/v1/company.staff.' + (isAdd ? 'create' : 'update'), body)
    }
  }
}
</script>