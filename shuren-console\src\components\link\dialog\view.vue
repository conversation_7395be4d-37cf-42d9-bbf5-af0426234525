<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    width="640px"
    class="el-link-dialog"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="onClosed"
  >
    <div style="display: flex; height: 344px">
      <el-radio-group v-model="name" size="small">
        <el-radio
          v-for="(item, index) in pages"
          :label="item.name"
          border
          :key="index"
          >{{ item.title }}</el-radio
        >
      </el-radio-group>

      <div style="flex: 1; padding-left: 20px; height: 100%">
        <el-channel
          v-model="channelId"
          v-if="name == 'media.channel'"
          checkStrictly
          @change="onChannel"
        ></el-channel>
        <media-select
          v-model="media.id"
          size="mini"
          v-else-if="name == 'media.detail'"
          @change="onMedia"
        >
          <el-image
            :src="media.cover_url"
            style="width: 100%; height: 240px; margin-top: 20px"
            fit="cover"
          ></el-image>
        </media-select>
        <el-input
          v-else-if="name == 'browser'"
          v-model="outerLink"
          type="textarea"
          maxlength="300"
          placeholder="以http://或https://开头，需要联网才能访问"
        ></el-input>
        <el-input
          v-else-if="name == 'links'"
          v-model="unOuterLink"
          type="textarea"
          maxlength="300"
          placeholder="联系管理员获取"
        ></el-input>
        <div
          v-else
          style="
            border: 1px solid #dcdfe6;
            height: 100%;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          {{ name }}
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false" size="small">取 消</el-button>
      <el-button type="primary" @click="submit" size="small">确 定</el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss">
.el-link-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .el-radio-group {
    width: 80px;
  }

  .el-radio {
    width: 80px;
    margin: 0;

    & + .el-radio {
      margin-top: 20px;
    }
  }

  .el-textarea,
  textarea,
  .el-cascader-panel,
  .el-cascader-menu__wrap,
  .el-cascader-menu {
    height: 100%;
  }

  .el-cascader-menu__wrap::-webkit-scrollbar {
    visibility: hidden;
  }

  .el-cascader-menu__wrap {
    overflow-x: hidden;
  }

  .el-cascader-menu {
    min-width: 160px;
  }

  .el-cascader-node__label {
    padding: 0;
  }

  .media-channel-panel {
    & > .el-input input {
      text-align: center;
    }

    .el-cascader-panel {
      height: 240px;
    }
  }

  .el-cascader-node__prefix {
    display: none;
  }
}
</style>

<script>
import ElChannel from "@/components/media/channel/panel";
import MediaSelect from "@/views/media/components/select";

export default {
  components: { ElChannel, MediaSelect },
  props: {
    model: {
      default: {},
    },
    title: {
      type: String,
      default: "跳转地址",
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let model = this.model;
    let channelId = null;
    let outerLink = "";
    let unOuterLink = '';
    let media = { id: null, type: "", alias: "", cover_url: "" };
    let name = model.name || "";

    switch (name) {
      case "media.channel":
        channelId = model.params.id;
        break;
      case "media.detail":
        media.id = model.params.id;
        break;
      case "browser":
        outerLink = model.query.url;
        break;
      case "links":
        unOuterLink = model.query.links;
        break;
    }

    return {
      name: name,
      channelId: channelId,
      channel: null,
      outerLink: outerLink,
      unOuterLink:unOuterLink,
      media: media,
      pages: [
        { name: "home", title: "首页" },
        { name: "dept", title: "组织架构" },
        { name: "system.setting", title: "系统设置" },
        { name: "media.channel", title: "频道栏目" },
        { name: "media.detail", title: "素材详情" },
        { name: "browser", title: "外部链接" },
        { name: "links", title: "内部链接" },
      ],
    };
  },
  methods: {
    submit() {
      let model = { name: this.name };

      switch (model.name) {
        case "media.channel":
          model.title = this.channel ? this.channel.name : "";
          model.params = { id: this.channelId };
          break;
        case "media.detail":
          model.title = this.media.title;
          model.image = this.media.cover_url;
          model.params = { id: this.media.id };
          break;
        case "browser":
          model.query = { url: this.outerLink };
          break;
        case "links":
          model.query = { links: this.unOuterLink };
          break;
      }

      this.visible = false;
      this.$emit("input", model);
      this.$emit("change", model);
    },
    onClosed() {
      this.$emit("update:visible", false);
      this.$emit("closed");
    },
    onMedia(media) {
      this.media.type = media ? media.type : "";
      this.media.alias = media ? media.alias : "";
      this.media.cover_url = media ? media.cover_url : "";
    },
    onChannel(data) {
      this.channel = data;
    },
  },
};
</script>
