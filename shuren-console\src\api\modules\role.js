import request from './request';

export default {
  query(query) {
    return request.get('/v1/sys.role.query', query)
  },
  list() {
    return request.get('/v1/sys.role.query')
  },
  add(data) {
    return request.post('/v1/sys.role.create', data)
  },
  save(data) {
    return request.post('/v1/sys.role.update', data)
  },
  delete(id) {
    return request.post('/v1/sys.role.delete', {id: id})
  }
};