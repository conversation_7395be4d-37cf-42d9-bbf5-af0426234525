<template>
  <div class="page-my-shop">
    <mytable ref="table" :api="$api.shop.my">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{$route.meta.title}}</div>
          <div class="toolbar-actions">
            <el-button type="primary" size="mini" @click="onAdd()">创建</el-button>
          </div>
        </div>
      </div>

      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="_status" label="状态" align="center" width="70"></el-table-column>
      <el-table-column prop="hotline" label="服务热线" width="220" align="center"></el-table-column>
      <el-table-column prop="leader_name" label="负责人" width="140" align="center">
        <div class="cell-leader" slot-scope="scope" @click="onLeader(scope.row)">{{scope.row.leader_name || '设置'}}</div>
      </el-table-column>
      <el-table-column prop="created" label="创建时间" width="220" align="center">
        <template slot-scope="scope">{{scope.row._created}}</template>
      </el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onDel(scope.row.id)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row.id)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <edit-leader v-model="leader"></edit-leader>
  </div>
</template>

<script>
  import EditLeader from './edit/leader';

  export default {
    name: 'page-my-shop',
    components: {EditLeader},
    data() {
      return {
        showLeader: 0,
        leader: null
      }
    },
    methods: {
      refresh() {
        this.$refs.table.refresh();
      },
      onAdd() {
        this.$router.push({name: 'shop.add'});
      },
      onLeader(item) {
        this.leader = item;
      },
      onEdit(id) {
        this.$router.push({name: 'shop.edit', params: {id: id}});
      },
      onDel(id) {
        this.$confirm('操作不可恢复，确定删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return this.$api.shop.destroy(id);
        }).then(this.refresh);
      }
    }
  }
</script>

<style lang="scss" src="./style.scss"></style>