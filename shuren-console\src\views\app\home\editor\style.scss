.editor-com {
  cursor: inherit;
  position: absolute;
  user-select: none;
  box-sizing: border-box;
  min-width: 1px;
  min-height: 1px;
  overflow: hidden;
  outline: none;
}

.editor-label {
  cursor: text;

  &:empty:before {
    // content: '在此处输入文字';
    color: #f0f0f0;
  }
}

.editor-select-link {
  z-index: 1;
  cursor: pointer;
  color: #fff;
  font-size: 32px;
  position: absolute;
  top: 0;
  right: 0;
  text-shadow: black 0 0 20px;
  filter: drop-shadow(black 0 0 20px);
  padding: 6px;
  display: none;
}

.editor-com:hover > .editor-select-link {
  display: block;
}

/*** 视频组件 ***/
.editor-video {
  .play-button {
    z-index: 1;
    width: 64px;
    height: 64px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 64px;
    color: #fff;
  }
}
.topBox{
  width: 100%;
  height: 100%;
  background-color: yellow;
}