<template>
  <div class="upload-body" v-loading="loading">
    <div class="upload-content">
      <div
          v-for="item in list"
          class="img-item"
          :class="{selected: selected.indexOf(item.id) > -1}"
          @click="toggleCheck(item)">
        <el-image :src="item.url" fit="cover"></el-image>
        <span class="icon"></span>
        <div class="image-meta">{{ item.width }}x{{ item.height }}</div>
      </div>
    </div>
    <div class="footer-action">
      <el-pagination
          @current-change="loadPage"
          :current-page="page"
          :page-size="limit"
          layout="total, prev, pager, next, jumper"
          :total="total">
      </el-pagination>
      <slot name="cancel"></slot>
      <el-button type="primary" @click="useImg" :disabled="selected.length == 0">确定({{ selected.length }})</el-button>
    </div>
  </div>
</template>

<script>
export default {
  inject: ['$dialog'],
  data() {
    return {
      loading: true,
      list: [],
      selected: [],
      checked: [],
      time: 0,
      offset: 0,
      limit: 18,
      page: 1,
      total: 0
    }
  },
  created() {
    this.loadPage(1);
  },
  methods: {
    toggleCheck(item) {
      let selected = this.selected, checked = this.checked, limit = this.$dialog.cnf.limit;

      if (limit == 1) {
        selected.splice(0, 1, item.id);
        checked.splice(0, 1, item);
        return;
      }

      let index = selected.indexOf(item.id);

      if (index == -1) {
        if (selected.length >= limit) {
          return this.$notify.warning({message: '最多可选择' + limit + '张图片', title: '提醒'});
        }

        selected.push(item.id);
        checked.push(item);
      } else {
        selected.splice(index, 1);
        checked.splice(index, 1);
      }
    },
    loadPage(page) {
      this.loading = true;

      let query = {creator: this.$store.state.admin.id, offset: (page - 1) * this.limit, limit: this.limit};

      this.$api.get('/v1/media.upload.images', query).then(res => {
        this.time = parseInt(Date.now() / 1000);
        this.page = page;
        this.total = res.count;
        this.list = res.rows;
      }).finally(() => {
        this.loading = false;
      });
    },
    useImg() {
      this.$emit('ok', this.checked);
    }
  }
}
</script>