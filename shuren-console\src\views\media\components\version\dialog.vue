<template>
  <el-dialog :visible.sync="visible" width="600px" class="media-version-dialog" custom-class="hide-header abs-close" @closed="closed">
    <el-table :data="list" size="mini" height="370px">
      <el-table-column prop="version" label="版本号" width="110px" align="center"></el-table-column>
      <el-table-column prop="_created" label="创建时间" width="120px"></el-table-column>
      <el-table-column prop="editor_name" label="创建人"></el-table-column>
      <el-table-column label="操作" width="90" align="center" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  export default {
    data() {
      return {
        visible: false,
        id: 0,
        type: '',
        list: [],
        operator: ''
      }
    },
    created() {
      this.refresh();
    },
    mounted() {
      this.visible = true;
    },
    methods: {
      refresh() {
        this.$api.media.version(this.id, this.type).then(list => this.list = list);
      },
      closed() {
        this.$destroy();
        this.$el.remove();
      },
      onDelete(item) {
        let auditor = this.operator == 'auditor';
        this.$confirm('操作不可恢复，确定删除吗?', '提示', {
          confirmButtonText: auditor ? '永久' : '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return auditor ? this.$api.media.audit.delete(item.id, item.type, item.version) : this.$api.media.delete(item.id, item.type, item.version);
        }).then(this.refresh);
      },
      onEdit(item) {
        this.$api.media.edit(this, item.id, item.type, item.version, this.operator == 'auditor' ? 'media.audit.edit' : 'media.edit');
        this.closed();
      }
    }
  }
</script>