<template>
    <div>
      <mytable ref="table" :list="list" :api="'/v1/mini.soldier.info.list'" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <!-- <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template> -->
            </div>
          </div>
        </div>
  
        <el-table-column label="姓名" prop="name" align="center" ></el-table-column>
        <el-table-column label="手机号" prop="mobile" align="center" ></el-table-column>
        <el-table-column label="立功" prop="honour" align="center" width="250" ></el-table-column>
        <el-table-column label="特长" prop="talent" align="center" width="250" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="190px">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row)" title="查看">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>


      <el-dialog title="军人信息" :visible.sync="visible" class="media-upload-video"
                 width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
                 :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules"  size="small" label-width="150px" auto-complete="off"
                 style="margin-right:40px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="model.name" ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">  
            <el-input v-model="model.mobile" ></el-input>
          </el-form-item>
          <el-form-item label="入伍时间" prop="join_time">
            <el-input v-model="model.join_time" ></el-input>
          </el-form-item>
          <el-form-item label="退伍时间" prop="out_time">
            <el-input v-model="model.out_time" ></el-input>
          </el-form-item>
          <el-form-item label="就业" prop="job">
            <el-input v-model="model.job" ></el-input>
          </el-form-item>
          <el-form-item label="立功" prop="family_address">
            <el-input v-model="model.honour" ></el-input>
          </el-form-item>
          <el-form-item label="特长" prop="talent">
            <el-input v-model="model.talent" ></el-input>
          </el-form-item>
        </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary"  size="small" @click="cancel">关闭</el-button>
        </div>
      </el-dialog>


    </div>
  </template>
    
  <script>

  export default {
    data() {
      return {
        query:{
        },
        visible:false,
        list:[],
        model:{}
      };
    },
    methods: {
      refresh() {
        this.$refs.table.refresh();
      },
      onDel(v){
          this.$confirm("操作不可恢复，确定删除吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              console.log(v);
              return this.$api.post('/v1/mini.soldier.info.delete', { id: v.id })
            })
            .then(this.refresh);
        
      },
     async onDetail(v){
          let res  = await this.$api.get('/v1/mini.soldier.info.query', { open_id: v.open_id });
          this.visible = true
          this.model = res
      },
      cancel(){
        this.visible = false
      },
      
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  </style>