<template>
  <div class="edit-shop-page" v-if="loading" v-loading="loading"></div>
  <edit-panel v-else :value="model"></edit-panel>
</template>

<script>
  import EditPanel from './panel';

  export default {
    components: {EditPanel},
    data() {
      return {
        loading: 1,
        model: {
          name: '',
          logo: '',
          cover_url: '',
          hotline: '',
          province_id: 0,
          city_id: 0,
          district_id: 0,
          address: '',
          lng: 0,
          lat: 0,
          status: 1,
          business_desc: '',
          intro: '',
          leader_name: '',
          leader_mobile: '',
          leader_wechat: ''
        }
      }
    },
    created() {
      let {name, params} = this.$route;
      if (name == 'shop.add') return this.loading = 0;

      this.$api.shop.get(params.id).then(res => {
        this.model = res;
        this.loading = 0;
      }).catch(_ => {
        this.$router.go(-1);
      });
    }
  }
</script>