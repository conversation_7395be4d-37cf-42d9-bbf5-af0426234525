import request from '../request';

let stateArr = [{key: 0, val: '待处理'}, {key: 1, val: '回访中'}, {key: 2, val: '有意向'}, {key: 3, val: '无意向'}];
let stateObj = stateArr.reduce((total, curr) => {
  return total[curr.key] = curr.val, total;
}, {});


export default {
  state(val) {
    return stateObj[val];
  },
  states: stateArr,
  query(query) {
    return request.get('about.contact.query', query);
  },
  save(id, remark, state) {
    return request.post('about.contact.save', {id, remark, state});
  },
  delete(id) {
    return request.post('about.contact.delete', {id});
  },
  get(id) {
    return request.get('about.contact.get?id=' + id);
  }
};