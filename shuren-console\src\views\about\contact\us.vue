<template>
  <div>
    <mytable ref="table" :api="$api.about.contact.query" :query="query">
      <el-form class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <el-date-picker
                title="提交时间"
                v-model="date"
                type="daterange"
                range-separator="至"
                start-placeholder="提交日期"
                end-placeholder="提交日期"
                value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']"
                align="right"
                size="mini"
                @change="onDate">
            </el-date-picker>
            <el-select v-model="query.state" size="mini" placeholder="全部状态" clearable>
              <el-option v-for="item in $api.about.contact.states" :label="item.val" :value="item.key"></el-option>
            </el-select>
            <el-button size="mini" icon="el-icon-search" native-type="submit" title="搜索" circle></el-button>
          </div>
        </div>
      </el-form>

      <el-table-column label="处理状态" prop="state" :formatter="fState" fixed="left" align="center" width="80px"></el-table-column>
      <el-table-column label="联系人" prop="name"></el-table-column>
      <el-table-column label="联系电话" prop="telphone"></el-table-column>
      <el-table-column label="微信号码" prop="wxnum"></el-table-column>
      <el-table-column label="企业名称" prop="enterprise"></el-table-column>
      <el-table-column label="合作内容" prop="business"></el-table-column>
      <el-table-column label="提交时间" prop="created" :formatter="fCreated"></el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action" fixed="right">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onDel(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="备注">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <edit-record v-if="editing" :model="editing" @closed="editing=null"></edit-record>
  </div>
</template>

<script>
import EditRecord from './components/edit-record';

export default {
  name: 'page-contact-record',
  components: {EditRecord},
  data() {
    return {
      radio1: "1",
      radio2: "2",
      query: {state: '', date: ''},
      date: null,
      editing: null
    }
  },
  methods: {
    refresh() {
      this.$refs.table.refresh();
    },
    fState(row, column, val) {
      return this.$api.about.contact.state(val);
    },
    fCreated(row, column, val) {
      return this.$utils.Date(val).format();
    },
    onDate(v) {
      this.query.date = v ? v[0] / 1000 + '_' + v[1] / 1000 : null;
    },
    onDel(row) {
      this.$confirm('操作不可恢复，确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return this.$api.about.contact.delete(row.id);
      }).then(this.refresh);
    },
    onEdit(row) {
      this.editing = row;
    },
  }
}
</script>

<style lang="scss">
.contact-edit-dialog {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>