<template>
  <div class="login-container">
    <div class="login-header">
      <div class="login-type">
        <label class="login-label" @click="$emit('change', 'password')">密码登录</label>
        <label class="login-label active">短信登录</label>
      </div>
      <div class="switch-type">
        <label class="switch-label">微信登录</label>
        <div class="qrcode" @click="$emit('change', 'weixin', agree)"></div>
      </div>
    </div>
    <el-form class="login-body" ref="form" :model="model" :rules="rules" @submit.native.prevent="submit">
      <el-form-item prop="mobile">
        <div class="area-select">中国+86</div>
        <el-input class="mobile" v-model.trim="model.mobile" placeholder="注册时填写的手机号" autofocus="true"
                  autocomplete="off" maxlength="11"></el-input>
      </el-form-item>
      <el-form-item prop="captcha">
        <el-input v-model.trim="model.captcha" maxlength="6" placeholder="短信验证码" autocomplete="off"></el-input>
        <el-button class="getsms" type="text" @click="sendsms" :disabled="disabled">{{ btn }}</el-button>
      </el-form-item>
      <div class="autologin">
        <el-checkbox v-model="model.keep_login" :true-label="3" :false-label="0">三天内自动登录</el-checkbox>
      </div>
      <el-button class="login-button" type="primary" native-type="submit" :disabled="!agree || loading">登 录</el-button>
    </el-form>
    <div class="login-footer">
      <agree v-model="agree"></agree>
      <div class="float-right">
        <a href="javascript:" class="forget">忘记密码</a>
        <router-link :to="register">免费注册</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import Agree from './agree';
export default {
  components: {Agree},
  props: ['loading'],
  data() {
    return {
      agree: true,
      btn: '获取短信验证码',
      disabled: 0,
      model: {
        by: 'mobile',
        country: '+86',
        mobile: '',
        captcha: '',
        keep_login: 3
      },
      rules: {
        mobile: {
          trigger: 'change', validator: function (rule, value, callback) {
            if (!value) {
              callback('请输入手机号');
            } else if (!validator.isMobilePhone(value, 'zh-CN', {strictMode: false})) {
              callback('手机号格式错误');
            } else {
              callback();
            }
          }
        },
        captcha: [
          {required: true, message: '请输入验证码', trigger: 'change'},
          {min: 4, max: 6, message: '长度在 4 到 6 个字符', trigger: 'change'}
        ]
      }
    }
  },
  computed: {
    register() {
      return {name: 'register', query: {redirect: this.$route.query.redirect}}
    }
  },
  methods: {
    sendsms() {
      this.$refs.form.validateField(['mobile'], (err) => {
        if (err || this.disabled) return;

        this.disabled = 1;
        this.$api.user.account.captcha('mobile login', this.model.country + this.model.mobile).then(this.daojishi).catch(() => {
          this.disabled = 0;
        });
      });
    },
    daojishi() {
      let index = 60;

      let timer = setInterval(() => {
        if (index < 1) {
          clearInterval(timer);
          this.btn = '重新获取验证码';
          this.disabled = 0;
        } else {
          this.btn = index-- + '秒后重新获取';
        }
      }, 1000);
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        this.$emit('submit', this.model);
      });
    }
  }
}
</script>