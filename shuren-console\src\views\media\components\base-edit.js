import Version from "@/views/media/components/version";

export default {
  inject: ['$page'],
  props: ['value'],
  watch: {
    author: {
      immediate: true,
      handler(v) {
        if (!this.model.author) this.model.author = v;
      }
    }
  },
  computed: {
    author() {
      return this.$store.state.admin.nickname
    }
  },
  created(){
    let {model} = this;
    model.id && setTimeout(() => {
      this.md5 = this.$utils.objToMd5(this.model);
    }, 3000);
  },
  methods: {
    onSave() {
      this.validate(this.doSave).then(res => {
        this.$message.success('已存稿');
        localStorage.removeItem('sub_Users')
        this.$page.refresh(res.id, res.type, res.draft);
      });
    },
    onPublish() {
      this.validate(this.doSave).then(res => {
        return this.$api.media.publish({id: res.id, type: res.type, version: res.draft});
      }).then(() => {
        localStorage.removeItem('sub_Users')
        this.$router.go(-1);
      });
    },
    doSave() {
      let {model} = this;
      if (model.id) {
        let md5 = this.$utils.objToMd5(model);
        if (md5 == this.md5) {
          return model;
        }
      }
      console.log(JSON.parse(localStorage.getItem('sub_Users'))[0].id);
      return this.$api.media.save(model);
    },
    showVersion() {
      Version.open(this.model.id, this.model.type);
    }
  }
}