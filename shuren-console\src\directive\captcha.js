export default function(el, binding,vnode){
 let $this=vnode.context;
  el.onclick = function(){
    if(el.hasAttribute('disabled') || el.classList.contains('disabled')){
      return;
    }
    event.preventDefault();
    event.stopPropagation();

    let values = binding.value;
    if(values.before){
      let $continue = binding.value.before();
      if(!$continue){
      	return;
			}
		}

    let data = {};
    for(let key in values){
    	if(key != 'before'){
    		data[key] = values[key];
			}
		}
    if(data.mobile !== undefined){
      if(data.mobile.toString().length !=11){
        return $this.$message.error('手机号错误');
      }
    }

    el.setAttribute('disabled', 'disabled');

    API.captcha.sms(data).then(() => {
      let number = 60;
      let timer = setInterval(function(){
        if(number == -1){
          clearInterval(timer);
          el.removeAttribute('disabled');
          el.innerHTML = '重新获取';
        }else{
          el.innerHTML = '重新获取('+number+'秒)';
          number--;
        }
      }, 1000);
    }).catch((e) => {
      el.removeAttribute('disabled');
    });
  }

}