<template>
  <div class="edit-base">
    <el-form ref="form" :model="model" :rules="rules" class="edit-panel-body" label-width="80px" label-suffix=":"
             size="small">
      <el-form-item label="标题" prop="title">
        <el-input v-model.trim="model.title" placeholder="请输入" maxlength="64" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="发布时间">
        <media-pubdate v-model="model.pubdate"></media-pubdate>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="记录人">
            <el-select v-model="model.staff_id" @change="personChange" filterable allow-create default-first-option>
              <el-option v-for="(item, index) in person" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="记录时间">
            <media-pubdate v-model="model.display_date" style="width:100%"></media-pubdate>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="链接地址">
        <media-detail-url v-model="model.detail_url"></media-detail-url>
      </el-form-item>
      <el-form-item label="音频来源">
        <media-source v-model="model.source"></media-source>
      </el-form-item>
      <el-form-item label="音频简介">
        <el-input type="textarea" v-model="model.abstract" :rows="6" maxlength="120"
                  placeholder="选填，摘要信息会帮助读者快速了解内容，如果不填写默认抓取正文前120个字">
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import MediaSource from '@/views/media/components/source';
import MediaPubdate from "@/views/media/components/pubdate";
import MediaDetailUrl from "@/views/media/components/detail-url";
import $request from '@/utils/request'

export default {
  components: { MediaSource, MediaPubdate, MediaDetailUrl },
  data() {
    return {
      rules: {
        title: { required: true, message: '请输入标题', trigger: 'blur' },
        author: { required: true, message: '请输入发布人', trigger: 'blur' }
      },
      person:[]
    }
  },
  computed: {
    model() {
      return this.$parent.model
    }
  },
  created() {
    $request.get('/v1/company.staff.list').then(res => {
      this.person = res
    })
  },
  methods: {
    validate(callback) {
      this.model.abstract = this.model.abstract.replace(/\s/g, '');
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    personChange() {

    }
  }
}
</script>