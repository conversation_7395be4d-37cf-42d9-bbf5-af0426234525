<template>
  <div class="staff-tag-select">
    <el-select
        v-model="input"
        placeholder="请选择"
        :size="size"
        :multiple="multiple"
        :clearable="clearable"
        :multiple-limit="limit"
        filterable
        @change="onChange">
      <el-option v-for="item in list" :label="item.name" :value="item.id"></el-option>
    </el-select>
    <div class="staff-tag-select-action">
      <el-button type="text" size="mini" v-if="showRefresh" @click.stop.prevent="reload">刷新</el-button>
      <el-button type="text" size="mini" v-if="showCreate" @click.stop.prevent="showEdit=true">新建</el-button>
    </div>

    <edit-dialog v-if="showEdit" :visible.sync="showEdit" @saved="reload"></edit-dialog>
  </div>
</template>

<style lang="scss">
.staff-tag-select:hover {
  position: relative;

  .staff-tag-select-action {
    display: inline-block;
  }
}

.staff-tag-select-action {
  z-index: 1;
  position: absolute;
  top: 2px;
  right: 2px;
  background: #fff;
  line-height: 1;
  padding-left: 10px;
  display: none;

  .el-button {
    padding: 7px 10px;

    & + .el-button:before {
      content: '';
      position: absolute;
      left: -7px;
      top: 50%;
      height: 12px;
      border-left: 1px solid #f0f0f0;
      transform: translateY(-50%);
    }
  }
}
</style>

<script>
import EditDialog from './edit-dialog';

export default {
  name: 'SelectStaffTag',
  components: {EditDialog},
  props: {
    multiple: Boolean,
    clearable: Boolean,
    size: 'mini',
    limit: 3,
    value: null,
    showRefresh: Boolean,
    showCreate: Boolean
  },
  data() {
    return {
      input: null,
      list: [],
      showEdit: false
    }
  },
  computed: {
    companyId() {
      return this.$store.state.admin.company_id;
    }
  },
  watch: {
    companyId: {
      immediate: true,
      handler() {
        this.reload();
      }
    },
    value: {
      immediate: true,
      handler(v) {
        this.input = v;
      }
    }
  },
  methods: {
    reload() {
      this.$api.get('/v1/company.staff.tag.list?company_id=' + this.companyId).then(list => {
        this.list = list;
      });
    },
    onChange(v) {
      this.$emit('input', v);
    }
  }
}
</script>