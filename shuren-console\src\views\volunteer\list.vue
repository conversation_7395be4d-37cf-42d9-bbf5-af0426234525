<template>
    <div>
      <mytable ref="table" :api="train_id ? '/v1/media.skill.trainees.list?train_id=' + train_id : '/v1/media.skill.trainees.list'"  :list="list" >
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              技能类别：<el-select v-model="value" placeholder="请选择" size="mini" @change="selectChange" clearable>
                    <el-option v-for="item in options" :key="item.id" :label="item.train_type" :value="item.id"> </el-option>
                </el-select>
              <!-- <el-button type="primary" size="mini" @click="onAdd">添加</el-button> -->
            </div>
          </div>
        </div>
        <el-table-column label="编号" prop="id" align="center"></el-table-column>
        <el-table-column label="姓名" prop="name" align="center"></el-table-column>
        <el-table-column label="年龄" prop="age" align="center"></el-table-column>
        <el-table-column label="性别" prop="sex" align="center"></el-table-column>
        <el-table-column label="电话" prop="phone" align="center"></el-table-column>
        <el-table-column label="家庭住址" prop="home" align="center"></el-table-column>
        <el-table-column label="操作" width="90" class-name="table-action">
          <template slot-scope="scope">
            <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
              </svg>
            </el-button>
            <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
              </svg>
            </el-button>
          </template>
        </el-table-column>
      </mytable>

      
      <el-dialog :title="dialogTitle" :visible.sync="visible" class="media-upload-video"
          width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
          :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules" size="small" label-width="150px" auto-complete="off"
                style="margin-right:40px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="model.name" ></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="model.sex" >
              <el-radio label="男">男</el-radio>
              <el-radio label="女">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input v-model="model.age" ></el-input>
          </el-form-item>
          <el-form-item label="电话" prop="phone">
            <el-input v-model="model.phone" ></el-input>
          </el-form-item>
          <el-form-item label="家庭住址" prop="home">
            <el-input v-model="model.home" ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm" >保存</el-button>
            <el-button @click="cancelEdit">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </template>
  <script>
  import request from "@/api/modules/request.js"
  export default {
    name: 'skill.detail',
    data() {
      return {
        list: [],
        flag: true,
        visible: false,
        model: {
          id: '',
          name: '',
          sex: '男',
          age: '',
          phone: '',
          home: '',
          train_id: this.train_id || null
        },
        options: [],
        value:'',
        train_id: null,
        rules: {
          name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
          sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
          age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入电话', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
          ],
          home: [{ required: true, message: '请输入家庭住址', trigger: 'blur' }]
        }
      };
    },
    created() {
      request.get('/v1/media.skill.list').then(res => {
        console.log(res);
        this.options = res.rows
        this.train_id = this.options[0].train_id
        
      })
    },
    mounted() {
    },
    methods: {
      validate(callback) {
        this.$refs.form.validate(valid => {
          valid && callback();
        });
      },
      refresh() {
        this.model = {
          staff_id: null,
          content: '',
          image_url: '',
          sort: null
        }
        this.$refs.table.refresh();
      },
      onAdd() {
        this.resetModel();
        this.dialogTitle = '添加培训人员';
        this.visible = true;
      },
      submitForm() {
        const isNew = !this.model.id;
        const apiUrl = isNew ? '/v1/media.skill.trainees.create' : '/v1/media.skill.trainees.update';
        
        // 准备提交的数据
        const submitData = {
          ...this.model,
          phone: this.model.mobile // 确保字段名匹配API要求
        };
        
        request.post(apiUrl, submitData).then(res => {
          this.$message.success(isNew ? '添加成功' : '更新成功');
          this.visible = false;
          this.$refs.table.refresh(); // 刷新列表
        }).catch(err => {
          this.$message.error(err.message || '操作失败');
        });
      },
      
      onEdit(row) {
        this.resetModel();
        this.model = { 
          ...row,
          train_id: this.train_id
        };
        this.dialogTitle = '编辑培训人员';
        this.visible = true;
      },
      onDelete(row) {
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          request.post('/v1/media.skill.trainees.delete', { id: row.id }).then(res => {
            this.refresh()
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      selectChange(v){
        if (v){
          this.value = this.options.filter(item => item.id == v)[0].train_type
          this.train_id = v
        }else{
          this.train_id = null
        }

        this.$nextTick(() => {
            this.refresh()
          })

      },
      // 重置表单模型
      resetModel() {
        this.model = {
          id: '',
          name: '',
          sex: '男',
          age: '',
          id_card: '',
          mobile: '',
          home: '',
          train_id: this.train_id || null
        };
      },
       // 取消编辑
      cancelEdit() {
        this.visible = false;
      },
      
      // 关闭前的回调
      beforeClose(done) {
        done();
      },
      
      // 关闭后的回调
      closed() {
        this.$refs.forms.resetFields();
      },
    },
  };
  </script>
  
  <style lang="scss" scoped></style>