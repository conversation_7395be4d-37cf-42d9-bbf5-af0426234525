<template>
  <el-select
      v-model="model"
      filterable
      clearable
      placeholder="请选择">
    <el-option v-for="user in list" :key="user.id" :label="fUser(user)" :value="user.id">
      <span style="float: left">{{ user.name }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">{{ user.position_title }}</span>
    </el-option>
  </el-select>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      list: []
    }
  },
  computed: {
    model: {
      get() {
        return this.value || null;
      },
      set(v) {
        this.value = v;
        this.$emit('input', v);
      }
    }
  },
  created() {
    let query = {company_id: this.$store.state.admin.company_id, offset: 0, limit: 100};
    this.$api.get('/v1/company.staff.search', query).then(list => {
      this.list = list;
    });
  },
  methods: {
    fUser(user) {
      user.position_title = (user.party_title + ' ' + user.position_name).split(' ').filter(str => str).join('、');
      return user.name + '(' + user.position_title + ')';
    }
  }
}
</script>