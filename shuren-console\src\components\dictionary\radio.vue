<template>
  <el-radio-group v-model="value" size="small">
    <el-radio v-for="item in list" :label="item.code" border>{{ item.name }}</el-radio>
  </el-radio-group>
</template>

<script>
export default {
  props: {
    value: {},
    type: {
      type: String
    }
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    this.$api.get('/v1/sys.dictionary.items', {type: this.type}).then(list => {
      this.list = list;
    });
  }
}
</script>