<template>
  <el-dialog
      class="uploadimg-dialog"
      :visible.sync="visible"
      :before-close="onClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="800px">

    <el-tabs v-model="activeName">
      <el-tab-pane label="本地上传" name="upload">
        <tab-upload @ok="onOk">
          <el-button @click="onClose" slot="cancel">取 消</el-button>
        </tab-upload>
      </el-tab-pane>
      <el-tab-pane label="网络图片" name="network" v-if="cnf.network">
        <tab-network @ok="onOk">
          <el-button @click="onClose" slot="cancel">取 消</el-button>
        </tab-network>
      </el-tab-pane>
      <el-tab-pane label="图片列表" name="images">
        <tab-list @ok="onOk">
          <el-button @click="onClose" slot="cancel">取 消</el-button>
        </tab-list>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import TabUpload from './upload';
import TabNetwork from './network';
import TabList from './list';

export default {
  components: {TabUpload, TabNetwork, TabList},
  provide() {
    return {
      '$dialog': this
    }
  },
  data() {
    return {
      visible: true,
      activeName: 'upload',
      cnf: null
    }
  },
  methods: {
    onClose() {
      this.visible = false;
      this.$emit('cancel', true);
    },
    onOk(list) {
      this.$emit('success', list.splice(0, this.cnf.limit));
      this.onClose();
    }
  }
}
</script>