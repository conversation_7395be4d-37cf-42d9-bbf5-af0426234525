<template>
  <div class="role-page" v-loading="loading">
    <div style="display: flex;">
      <!-- 角色分组 -->
      <div class="left-tree">
        <el-tree ref="tree" :data="roles" :indent="0" :props="defaultProps" @node-click="onRole" default-expand-all></el-tree>
      </div>

      <div style="flex:1">
        <el-form ref="form" label-width="80px" :inline="true" size="small">
          <el-form-item label="角色名称">
            <el-input v-model.trim="model.name" maxlength="8">
              <el-checkbox slot="suffix" v-model="model.enabled" :true-label="1" :false-label="0" class="is-enabled">启用</el-checkbox>
            </el-input>
          </el-form-item>
          <el-form-item label="所属分组">
            <el-input v-model.trim="model.group" maxlength="8" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-dropdown v-if="model.id" split-button type="primary" size="small" @click="submit('save')" @command="submit">
              保存
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="add">另存</el-dropdown-item>
                <el-dropdown-item command="del">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" icon="el-icon-edit" @click="submit('add')" v-else>新建</el-button>
          </el-form-item>
        </el-form>

        <el-table ref="table" :data="menus" size="mini" :show-header="false">
          <el-table-column prop="title2" label="菜单名称" width="160"></el-table-column>
          <el-table-column prop="nodes" label="API节点">
            <template slot-scope="scope">
              <el-checkbox v-if="scope.row.isLeaf" v-model="scope.row.checked" :disabled="scope.row.disabled">页面</el-checkbox>
              <el-checkbox v-for="node in scope.row.nodes" :key="node.id"
                           v-model="node.checked"
                           :disabled="node.disabled"
                           @change="onCheck(node)">{{ node.title }}
              </el-checkbox>
            </template>
          </el-table-column>
          <el-table-column width="80" align="center">
            <el-button slot-scope="scope" v-if="scope.row.isLeaf && scope.row.nodes" type="text" size="mini" @click="onDB(scope.row)">数据权限</el-button>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 数据权限 -->
    <el-dialog class="role-db-dialog" v-if="menu" title="数据权限" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false">
      <el-table :data="menu.nodes" size="small" style="margin-bottom:20px">
        <el-table-column label="功能" prop="title" width="120px"></el-table-column>
        <el-table-column label="权限" prop="access">
          <el-radio-group slot-scope="scope" v-model="scope.row.access" @change="onNode(scope.row)">
            <el-radio :label="0">无权限</el-radio>
            <el-radio :label="1">仅自己</el-radio>
            <el-radio :label="2">不限制</el-radio>
          </el-radio-group>
        </el-table-column>
      </el-table>
      <el-alert title="需要后台做支持后方可生效，一般只需设置读取、修改、删除即可" type="warning" :closable="false" center></el-alert>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      roles: [],
      menus: [],
      loading: false,
      editTitle: '',
      model: {id: 0, name: '', enabled: 0, group: ''},
      dialogVisible: false,
      menu: null,
      defaultProps: {label: 'name', children: 'list'}
    }
  },
  created() {
    this.reload();
  },
  methods: {
    reload() {
      this.loading = true;
      Promise.all([
        this.$api.role.query(),
        this.$api.menu.query()
      ]).then(([roles, menus]) => {
        this.loading = false;
        this.roles = this.groupRoles(roles);
        this.menus = menus.map(menu => {
          if (!menu.alias) menu.disabled = 1;

          menu.checked = false;
          menu.nodes && menu.nodes.forEach(node => {
            if (!node.alias) node.disabled = 1;
            node.checked = false;
            node.access = 0;
          });
          return menu;
        });
      });
    },
    groupRoles(rows) {
      let keys = [];
      let list = [];
      let index, name;

      rows.forEach(role => {
        if (role.group) {
          name = role.group;
          index = keys.indexOf(name);

          if (index == -1) {
            index = keys.push(name) - 1;
            list.push({name: name, list: []});
          }

          list[index].list.push(role);
        } else {
          keys.push(role.name + ':' + role.id);
          list.push(role);
        }
      });

      return list;
    },
    onRole(role) {
      if (!role.id) return;

      this.model = {id: role.id, name: role.name, enabled: role.enabled, group: role.group};
      this.menus.forEach(item => {
        item.checked = role.menus.indexOf(item.alias) != -1;
        item.nodes && item.nodes.forEach(node => {
          node.checked = !!role.nodes[node.alias];
          node.access = role.nodes[node.alias] || 0;
        });
      });
    },
    onDB(menu) {
      this.menu = menu;
      this.dialogVisible = true;
    },
    onCheck(node) {
      if (node.checked) {
        if (node.access == 0) node.access = 1;
      } else {
        node.access = 0;
      }
    },
    onNode(node) {
      node.checked = node.access !== 0;
    },
    submit(action) {
      !this.loading && this[action]();
    },
    getModel() {
      let {model} = this, menus = [], nodes = {};
      if (!model.name) return this.$message({type: 'warning', message: '请填写角色名称'}), false;

      this.menus.forEach(menu => {
        if (menu.checked) menus.push(menu.alias);

        menu.nodes && menu.nodes.forEach(node => {
          if (node.checked) nodes[node.alias] = node.access;
        });
      });

      this.loading = true;
      return Object.assign({}, model, {menus: menus, nodes: nodes});
    },
    add() {
      let model = this.getModel();
      if (!model) return;
      delete model.id;
      this.$api.role.add(model).then(this.reload).catch(() => this.loading = 0);
    },
    save() {
      let model = this.getModel();
      if (!model) return;
      this.$api.role.save(model).then(this.reload).catch(() => this.loading = 0);
    },
    execDel() {
      this.$api.role.delete(this.model.id).then(this.reload);
    },
    del() {
      this.$confirm('操作不可恢复，确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(this.execDel);
    }
  }
}
</script>

<style lang="scss">
.role-page {
  .is-enabled {
    top: 0;
    right: 10px;
  }

  .left-tree {
    padding-right: 20px;
    width: 200px;

    &:after {
      content: '';
      position: absolute;
      right: 20px;
      top: 0;
      bottom: 0;
      border-left: 4px solid #f0f0f0;
    }
  }
}

.role-db-dialog {
  .el-dialog__body {
    padding: 10px 20px 20px 20px;
  }

  .el-radio {
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>