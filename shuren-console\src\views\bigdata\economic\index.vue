<template>
  <div>
    <mytable ref="table" api="/v1/economic.info.query" :query="query" :list="list">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">
            类型：<el-select v-model="values" placeholder="请选择" size="mini" @change="selectChange">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="toolbar-actions">
            <el-input class="search-val" size="mini" v-model="query.company_name" placeholder="请输入公司名称" style="width: 200px" maxlength="5" clearable> </el-input>
            <el-button size="mini" icon="el-icon-search" title="搜索" circle @click="searchFun"></el-button>
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>

      <el-table-column label="编号" prop="id" align="center"></el-table-column>
      <!-- <el-table-column label="父编号" prop="parent_code"></el-table-column> -->
      <el-table-column label="单位名称" prop="company_name" align="center"></el-table-column>
      <el-table-column label="法人" prop="legal_person" align="center"></el-table-column>
      <el-table-column label="地址" prop="address" align="center"></el-table-column>
      <el-table-column label="电话" prop="phone" align="center"></el-table-column>
      <el-table-column label="网格名称" prop="grid_name" align="center"></el-table-column>
      <el-table-column label="排序" prop="sort" align="center"></el-table-column>
      <el-table-column label="操作" width="200" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
    <el-dialog :title="flag ? '添加单位' : '编辑单位'" :visible.sync="dialogVisible" width="500px" @closed="onClosed" :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="80px" :model="model">
        <el-form-item label="单位名称" prop="company_name">
          <el-input v-model.trim="model.company_name" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="法人" prop="legal_person">
          <el-input v-model.trim="model.legal_person" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model.trim="model.address" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model.trim="model.phone" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="所属网格" prop="grid_name">
          <el-radio v-model="model.grid_name" label="望凤街网格">望凤街网格</el-radio>
          <el-radio v-model="model.grid_name" label="中兴街网格">中兴街网格</el-radio>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model.number="model.sort" placeholder="必填项" onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/api/modules/request.js'
export default {
  name: 'economic',

  data() {
    return {
      list: [],
      flag: true,
      dialogVisible: false,
      model: {
        grid_name: '',
        sort: 0,
        company_name: '',
        id: '',
        type: '0',
        legal_person: '',
        address: '',
        phone: '',
      },
      query: {
        company_name: '',
        type: '1',
      },
      values: '1',
      options: [
        {
          value: '1',
          label: '事业单位',
        },
        {
          value: '2',
          label: '社会团体',
        },
        {
          value: '3',
          label: '企业',
        },
        {
          value: '4',
          label: '个体户',
        },
      ],
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    searchFun() {
      request.get('/v1/economic.info.query?company_name=' + this.query.company_name + '&&type=' + this.query.type).then((res) => {
        // console.log(res);
        this.list = res.rows
      })
    },
    refresh() {
      this.$refs.table.refresh()
    },
    onAdd() {
      this.flag = true
      // this.model = {}
      this.model.id = 0
      this.model.code = ''
      this.model.name = ''
      this.model.sort = 0
      this.model.remark = ''
      this.dialogVisible = true
    },
    onSubmit() {
      if (this.flag) {
        // console.log(this.model)
        this.model.type = this.query.type
        request.post('/v1/economic.info.create', this.model).then((res) => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        // console.log(this.model)
        this.model.type = this.query.type
        request.post('/v1/economic.info.update', this.model).then((res) => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.model = { ...row }
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          request.post('/v1/economic.info.delete', { id: row.id }).then((res) => {
            this.refresh()
            this.$message({
              type: 'success',
              message: '删除成功!',
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    selectChange(e) {
      console.log(e, this.values)
      switch (e) {
        case '1':
          this.query.type = 1
          break
        case '2':
          this.query.type = 2
          break
        case '3':
          this.query.type = 3
          break
        default:
          this.query.type = 4
          break
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
