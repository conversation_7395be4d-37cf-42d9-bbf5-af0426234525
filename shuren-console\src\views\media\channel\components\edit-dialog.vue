<template>
  <!-- 编辑 -->
  <el-dialog :visible.sync="visible" width="700px" custom-class="dialog-form abs-close edit-media-channel-dialog"
             @closed="$emit('closed')" :append-to-body="true" :close-on-press-escape="false"
             :close-on-click-modal="false">
    <template slot="title">
      <span class="el-dialog__title">{{
        model.platform ? "平台" : "编辑"
      }}</span>
      <div class="el-dialog__headerbtn">
        <el-switch v-model="model.hidden" :active-value="0" :inactive-value="1" title="是否显示"></el-switch>
      </div>
    </template>

    <el-form ref="form" label-width="70px" :model="model" :rules="rules" v-loading="loading" size="small"
             style="display: flex">
      <div style="flex: 1">
        <el-form-item label="上 级" prop="pid">
          <el-cascader placeholder="一级" filterable clearable v-model="parents" :options="cascader" :props="{
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }">
          </el-cascader>
        </el-form-item>
        <el-form-item label="名 称" prop="name">
          <el-input v-model="model.name" maxlength="8" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="类 型" prop="type">
          <el-radio-group v-model="model.type" size="mini">
            <el-radio v-for="item in $api.media.channel.types" :label="item.key" border>{{ item.val }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模 板">
          <el-select v-model="model.template">
            <el-option v-for="item in templates" :label="item.val" :value="item.key">
              <span style="float: left">{{ item.val }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px" @click.stop.prevent="seeImg(item.img)">查看</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规 则">
          <el-row>
            <el-col :span="12" style="padding-right: 10px">
              <el-select v-model="model.first_sort">
                <el-option label="序号越小越靠前" :value="0"></el-option>
                <el-option label="序号越大越靠前" :value="1"></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" style="padding-left: 10px">
              <el-select v-model="model.second_sort">
                <el-option label="创建越早越靠前" :value="0"></el-option>
                <el-option label="创建越晚越靠前" :value="1"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </div>
      <div style="width: 186px; margin-left: 20px">
        <el-image :src="model.logo" fit="cover" style="width: 186px; height: 186px" @click.native="upLogo">
          <div class="el-image__error" slot="error">logo</div>
        </el-image>
        <el-input class="sort" size="small" type="number" v-model.number="model.sort" placeholder="显示顺序" maxlength="2"
                  style="margin-top: 10px" title="正序排序，数字越小越靠前，1-999">
        </el-input>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import SelectImage from "@/views/media/image/select";

export default {
  props: ["value", "channels", "platform"],
  data() {
    let model = this.value
      ? Object.assign({}, this.value, { children: undefined })
      : {
        owner: this.$store.state.admin.id,
        type: 0,
        name: "",
        hidden: 0,
        pid: 0,
        logo: "",
        sort: "",
        parents: "",
        platform: this.platform ? 1 : 0,
        first_sort: 1,
        second_sort: 1,
        template: 0,
      };

    let cascader = JSON.parse(JSON.stringify(this.channels));
    let parents = this.getParents(cascader, model.id);
    let cnd = this.$env.CDN_URL;

    return {
      visible: true,
      loading: false,
      model: model,
      cascader: cascader,
      parents: parents,
      rules: {
        name: { require: true, message: "必填项", trigger: "blur" },
      },
      templates: [
        { key: 0, val: "模板一", img: cnd + "/img/20210126/58936993/311.jpg" },
        { key: 1, val: "模板二", img: cnd + "/img/20210126/58936229/494.jpg" },
        { key: 2, val: "模板三", img: cnd + "/img/20210126/58936596/513.jpg" },
        { key: 3, val: "模板四", img: cnd + "/img/20210126/58936839/351.jpg" },
      ],
      bool: false
    };
  },
  created() {
    console.log(this.channels);
    if (this.channels.length == 0) return
    this.bool = this.channels.some(v => {
      return v.children
    })
    if (this.bool == false) {
      this.$notify({
        title: '提示',
        message: '请至少添加一个二级频道后再保存，否则无法保存',
        type: 'warning',
        showClose: false
      });
    }
  },
  methods: {
    getParents(list, id) {
      let parents = [];
      this.eachChannel(list, id, [], function (rows) {
        parents = rows;
      });
      return parents;
    },
    eachChannel(list, editId, parents, callback) {
      for (let i = list.length - 1, item; i > -1; i--) {
        item = list[i];

        if (item.type) {
          item.disabled = true;
        }

        if (item.id == editId) {
          item.disabled = true;
          delete item.children;
          callback(parents);
        } else if (item.children) {
          this.eachChannel(
            item.children,
            editId,
            parents.concat(item.id),
            callback
          );
        }
      }
    },
    upLogo() {
      SelectImage(1).then((res) => {
        this.model.logo = res[0].url;
      });
    },
    submit() {
      if (this.channels.length != 0 && this.parents == "" && this.bool == false) {
        this.$message({
          type: "error",
          message: "请添加二级频道！添加二级频道即可保存"
        })
        return;
      }
      this.$refs.form.validate((valid) => {
        if (!valid || this.loading) return;

        this.loading = true;
        let { model } = this;
        console.log(model);
        model.pid = this.parents[this.parents.length - 1] || 0;
        this.$api.media.channel[model.id ? "update" : "create"](this.model)
          .then((_) => {
            this.visible = false;
            this.$emit("change");
          })
          .catch((_) => {
            this.loading = false;
          });
      });
    },
    seeImg(url) {
      window.open(url);
    },
  },
};
</script>

<style lang="scss">
.edit-media-channel-dialog {
  .sort input {
    text-align: center;
  }

  .el-radio {
    width: 98px;
  }
}
</style>