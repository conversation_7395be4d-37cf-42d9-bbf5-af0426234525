<template>
  <!-- 编辑 -->
  <el-dialog :visible.sync="visible"
             width="600px"
             title="组织架构"
             custom-class="dialog-form abs-close"
             @closed="$emit('closed')"
             :append-to-body="true"
             :close-on-press-escape="false"
             :close-on-click-modal="false">

    <el-form ref="form" label-width="90px" :model="model" :rules="rules" v-loading="loading" size="small">
      <el-row>
        <el-col :span="15">
          <el-form-item label="部门名称" prop="name">
            <el-input v-model.trim="model.name" maxlength="20" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="部门编码" prop="code">
            <el-input v-model.trim="model.code" maxlength="20" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="15">
          <el-form-item label="办公电话" prop="phone">
            <el-input v-model.trim="model.phone" maxlength="15" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="显示顺序" prop="sort">
            <el-input v-model.number="model.sort" placeholder="正序排序" maxlength="2"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="部门主管" prop="leader_id">
        <el-staff v-model="model.leader_id"></el-staff>
      </el-form-item>

      <el-form-item label="上级部门" prop="pid">
        <el-cascader placeholder="请选择" filterable clearable
                     v-model="parents" :options="cascader"
                     :props="{label: 'name', value: 'id', disabled: 'disabled', expandTrigger: 'hover', checkStrictly: true}">
        </el-cascader>
      </el-form-item>

      <el-form-item label="部门简介" prop="intro">
        <el-input type="textarea" :rows="8" v-model.trim="model.intro" placeholder="请输入" maxlength="500" show-word-limit></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ElStaff from '@/components/staff/select';

export default {
  components: {ElStaff},
  props: ['value', 'channels'],
  data() {
    let model = this.value ? Object.assign({}, this.value, {children: undefined}) : {
      company_id: this.$store.state.admin.company_id,
      name: '',
      code: '',
      pid: 0,
      sort: '',
      phone: '',
      leader_id: 0,
      leader_name: '',
      intro: ''
    };

    let cascader = JSON.parse(JSON.stringify(this.channels));
    let parents = this.getParents(cascader, model.id);

    return {
      visible: true,
      loading: false,
      cascader: cascader,
      model: model,
      parents: parents,
      rules: {
        name: {required: true, message: '必填项', trigger: 'blur'}
      }
    }
  },
  methods: {
    getParents(list, id) {
      let parents = [];
      this.eachChannel(list, id, [], function (rows) {
        parents = rows;
      });
      return parents;
    },
    eachChannel(list, editId, parents, callback) {
      for (let i = list.length - 1, item; i > -1; i--) {
        item = list[i];

        if (item.id == editId) {
          item.disabled = true;
          delete item.children;
          callback(parents);
        } else if (item.children) {
          this.eachChannel(item.children, editId, parents.concat(item.id), callback);
        }
      }
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid || this.loading) return;

        this.loading = true;
        let {model} = this;
        model.pid = this.parents[this.parents.length - 1];
        this.$api.department[model.id ? 'update' : 'create'](this.model).then(_ => {
          this.visible = false;
          this.$emit('change');
        }).catch(_ => {
          this.loading = false
        });
      });
    }
  }
}
</script>
