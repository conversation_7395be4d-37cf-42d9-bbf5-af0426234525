<template>
  <el-cascader
      v-model="model"
      :props="props"
      :size="size"
      :clearable="clearable"
      :placeholder="placeholder"
  ></el-cascader>
</template>

<script>
export default {
  name: '<PERSON>Address',
  props: {
    province: {
      type: Number,
      default: 0
    },
    city: {
      type: Number,
      default: 0
    },
    district: {
      type: Number,
      default: 0
    },
    size: {
      default: 'small'
    },
    level: {
      default: 3
    },
    clearable: {
      default: true
    },
    placeholder: {
      default: '请选择'
    }
  },
  computed: {
    model: {
      get() {
        return [this.province, this.city, this.district].splice(0, this.level);
      },
      set(v) {
        this.province = v[0] || 0;
        this.city = v[1] || 0;
        this.district = v[2] || 0;
        this.$emit('update:province', this.province);
        this.$emit('update:city', this.city);
        this.$emit('update:district', this.district);
      }
    },
    props() {
      let scope = this;

      return {
        label: 'name', value: 'id', lazy: true, lazyLoad(node, resolve) {
          let {level, data} = node, leaf = level + 1 >= scope.level;

          scope.$api.address.city(level === 0 ? 0 : data.id).then(list => {
            list.forEach(item => {
              item.leaf = leaf;
            });

            resolve(list);
          });
        }
      };
    }
  }
}
</script>