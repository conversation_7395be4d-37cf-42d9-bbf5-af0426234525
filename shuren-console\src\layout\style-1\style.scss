$max-width: 100%;
$min-width: 768px;
$padding: 14px;

.layout-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.layout-sidebar {
  z-index: 10;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 220px;
  height: 100%;
  background: #304156;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  color: #bfcbd9;

  .sidebar-container {
    flex: 1;
    overflow-y: auto;
  }

  .sidebar-header {
    height: 56px;
    padding: 0 $padding;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #1f2d3d;
    cursor: pointer;
  }

  .sidebar-nickname {
    flex: 1;
    margin-left: 15px;
  }

  .menu-group:hover {
    background: rgba(31, 45, 61, .2);
  }

  .menu-list .menu-list {
    background: #1f2d3d
  }
}

.sidebar-avatar {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .actions {
    padding: 60px;

    .el-button {
      width: 100px;
      height: 100px;

      & + .el-button {
        margin-left: 30px;
      }
    }
  }
}

.layout-header {
  z-index: 2;

  .content {
    background: #fff;
    box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.1);
  }
}

.layout-body {
  margin-left: 220px;
}

.layout-content {
  margin-top: 2px;
  height: calc(100vh - 57px);
  overflow: auto;
}

.layout-view {
  min-height: calc(100vh - 116px);
  overflow: visible;
  background: #fff;
  padding: 20px;
}

.layout-footer {
  padding: 19px 0;
  background: #fff;
  border-top: 2px solid #f0f0f0;

  .copyright {
    color: #8a8a8a;
    text-align: center;
  }
}

.layout-tabs {
  height: 55px;
  background: #fff;
  display: flex;
  align-items: center;
  user-select: none;

  .tabs-left, .tabs-right {
    height: 100%;
    cursor: pointer;
    width: 30px;
    justify-content: center;
    display: flex;
    align-items: center;

    &:hover {
      background: #ecf5ff;
    }
  }

  .tabs-list {
    position: relative;
    flex: 1;
    overflow-x: auto;
    overflow-y: visible;
    height: 100%;
    display: flex;

    &::-webkit-scrollbar {
      display: none
    }
  }

  .tabs-item {
    cursor: pointer;
    flex-shrink: 0;
    position: relative;
    height: 100%;
    padding: 0 20px;
    display: flex;
    width: auto;
    align-items: center;
    justify-content: center;
    min-width: 96px;

    &.active, &:hover {
      .tabs-name {
        z-index: -1;
        visibility: hidden;
        pointer-events: none;
      }

      .tabs-full-name {
        display: block;
      }
    }

    &.active {
      cursor: initial;
      background: #f8f8f8;
      border-bottom: 1px solid #1f2d3d;

      .scroll-name:first-child {
        animation: auto-scroll-1 8s linear infinite;
      }

      .scroll-name:last-child {
        animation: auto-scroll-2 8s linear infinite;
      }
    }

    &:hover {
      .el-icon-circle-close {
        display: inline-block;
      }

      &:not(.active) {
        .scroll-name:first-child {
          animation: auto-scroll-1 3s linear infinite;
        }

        .scroll-name:last-child {
          animation: auto-scroll-2 3s linear infinite;
        }
      }
    }
  }

  .tabs-full-name {
    display: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: clip;
    position: absolute;
    left: 10px;
    right: 10px;
  }

  .scroll-name {
    display: inline-block;
    padding-right: 20px;
  }

  .el-icon-circle-close {
    display: none;
    z-index: 1;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;

    &:hover {
      color: #409EFF;
    }
  }
}

@media screen and (max-width: 1200px) {
  .layout-sidebar {

  }

  .layout-body {

  }
}
