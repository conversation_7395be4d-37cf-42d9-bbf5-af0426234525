<template>
  <el-dialog title="编辑类别" :visible.sync="visible"
             :append-to-body="true" :close-on-press-escape="false"
             width="500px" class="dialog-form"
             @closed="$emit('closed')">
    <el-form label-width="85px" size="small">
      <el-form-item prop="code" label="Code">
        <el-input placeholder="唯一编码" v-model="model.code" maxlength="20" :disabled="!!model.id"></el-input>
      </el-form-item>
      <el-form-item prop="name" label="Name">
        <el-input placeholder="编码含义" v-model="model.name" maxlength="8"></el-input>
      </el-form-item>
      <el-form-item prop="type" label="Type">
        <el-radio-group v-model="model.type">
          <el-radio :label="1">数字</el-radio>
          <el-radio :label="0">字符串</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="remark" label="Remark">
        <el-input type="textarea" :rows="1" v-model="model.remark" placeholder="系统备注" maxlength="50"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="danger" v-if="model.id" @click="onDelete">删 除</el-button>
      <el-button v-else @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      visible: true,
      model: this.value ? Object.assign({}, this.value) : {
        code: '',
        name: '',
        type: 1,
        remark: ''
      }
    }
  },
  methods: {
    submit() {
      this.visible = false;

      let {model} = this;
      let url = '/v1/sys.dictionary.' + (model.id ? 'update' : 'create') + '?action=type';
      this.$api.post(url, model).then(res => {
        let list = this.$parent.types;

        list.some(item => {
          if (item.id == res.id) {
            Object.assign(item, res);
            return true;
          }
        }) || list.splice(0, 0, res);

        this.$parent.current = res;
      });
    },
    onDelete() {
      this.$confirm('删除后将自动清空所有值, 是否继续?', '操作不可恢复', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.visible = false;
        let list = this.$parent.types;
        let {id} = this.model;

        this.$api.post('/v1/sys.dictionary.delete?action=type', {id: id}).then(_ => {
          list.some((item, index) => {
            if (item.id == id) {
              list.splice(index, 1);
              this.$nextTick(() => {
                this.$parent.current = list[index] || list[index - 1];
              });
              return true;
            }
          });
        });
      });
    }
  }
}
</script>