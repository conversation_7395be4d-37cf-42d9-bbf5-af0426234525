import Env from './env'
import EventEmitter from './EventEmitter'

const http = axios.create({
  baseURL: Env.APP_MYNATAPP,
  timeout: 30000,
  withCredentials: false,
  crossDomain: false,
  responseType: 'json',
})
http.interceptors.request.use(
  (config) => {
    if (!/^http(s?):\/\//.test(config.url) || config.url.startsWith(config.baseURL)) {
      config.selfUrl = true
      config.headers['Authorization'] = Env.token
    }
    return config
  },
  (error) => {
    scope.emit('error', error)
    return Promise.reject(error)
  }
)

http.interceptors.response.use(
  (response) => {
    let cnf = response.config
    if (cnf.selfUrl) Env.token = response.headers['authorization']

    if (cnf.method == 'head') return response.headers

    let res = response.data,
      type = cnf.responseType

    if ((!res && type == 'json') || (type != 'json' && typeof res == 'object')) {
      return Promise.reject(new Error('网络连接失败'))
    }

    if (!cnf.selfUrl || type != 'json') return res

    if (res.code == 200) {
      return res.data
    } else {
      const e = new Error(res.message || '网络连接失败')
      e.code = res.code
      scope.emit('error', e)
      return Promise.reject(e)
    }
  },
  (error) => {
    scope.emit('error', error)
    return Promise.reject(error)
  }
)
function request(options) {
  return http(options)
}

const scope = EventEmitter(request)

scope.get = function (url, params) {
  return http({ url: url, method: 'get', params: params })
}

scope.post = function (url, data) {
  return http({ url: url, method: 'post', data: data })
}

scope.head = function (url, params) {
  return http({ url: url, method: 'head', params: params })
}

export default request
