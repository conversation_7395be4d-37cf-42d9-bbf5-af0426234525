import request from './request';
import utils from '@/utils';

function query(params) {
  return request({url: 'shop.query', method: 'get', params: params});
}

const status = {
  0: '装修中',
  1: '营业中',
  2: '已打样',
  3: '已注销'
};

function all(query) {
  return request.get('shop.all', query).then(res => {
    res.rows.forEach(item => {
      item._created = utils.Date(item.created).format();
      item._status = status[item.status] || '未知';
    });
    return res;
  });
}

function my(query) {
  return request.get('shop.my', query).then(list => {
    list.forEach(item => {
      item._created = utils.Date(item.created).format();
      item._status = status[item.status] || '未知';
    });

    return list;
  });
}

function get(id) {
  return request.get('shop.get?id=' + id);
}

function add(data) {
  return request.post('shop.add', data);
}

function edit(data) {
  return request.post('shop.edit', data);
}

function destroy(id) {
  return request.post('shop.delete', {id: id});
}

function setLeader(data) {
  return request.post('shop.setLeader', data);
}

export default {all, query, get, add, edit, status, destroy, setLeader, my};