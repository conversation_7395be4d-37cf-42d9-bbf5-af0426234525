import SelectImage from '@/views/media/image/select'
import SelectLink from '@/components/link/dialog'
import SelectVideo from '@/views/media/video/select'

export default {
  props: ['model'],
  // created()
  // {
  //   console.log(this.model)
  // },
  methods: {
    baseStyle() {
      let model = this.model
      return `width:${model.width}px; height:${model.height}px;`
    },
    borderStyle() {
      let border = this.model.border
      if (!border.enabled) return ''
      return `border: ${border.width}px ${border.style} ${border.color};border-radius:${border.radius}px;`
    },
    transformStyle() {
      let model = this.model
      return `transform: translate(${model.x}px, ${model.y}px) rotate(${model.r}deg);`
    },
    shadowStyle() {
      let shadow = this.model.shadow,
        value = `${shadow.color} ${shadow.x}px ${shadow.y}px ${shadow.b}px`
      return this.$options.name == 'EditorLabel' ? `text-shadow: ${value};` : `filter: drop-shadow(${value});`
    },
    backgroundStyle() {
      let bg = this.model.background,
        position,
        repeat,
        size
      switch (bg.size) {
        case 'repeat':
          repeat = 'repeat'
          position = 'unset'
          size = 'auto'
          break
        case 'cover':
        case 'contain':
          repeat = 'no-repeat'
          position = '50% 50%'
          size = bg.size
          break
      }

      return `background:${repeat} ${bg.color} url('${bg.image}') ${position}/${size};`
    },
    fontStyle() {
      let font = this.model.font
      return `color: ${font.color};font-size: ${font.size}px;font-weight: ${font.weight};line-height:${font.height}px;text-align:${font.align};`
    },
    getStyle() {
      return this.baseStyle() + this.transformStyle() + this.borderStyle() + this.shadowStyle() + this.backgroundStyle()
    },
    onBgImage(e) {
      e.preventDefault()
      e.stopPropagation()

      let el = e.target,
        width = el.clientWidth,
        height = el.clientHeight

      SelectImage(1, {
        accept: 'image/jpeg,image/png',
        placeholder: '建议尺寸' + width + 'x' + height + '像素，文件大小不超过300KB',
        compress: 0,
      }).then((list) => {
        this.model.background.image = list[0].url
      })
    },
    onLink() {
      let { model } = this
      let link = model.link || {}

      SelectLink({
        name: link.name,
        params: link.params,
        query: link.query,
      }).then((to) => {
        link.name = to.name
        link.params = to.params
        link.query = to.query
        if (!model.link) model.link = link

        if (!!to.image && !model.background.image) {
          model.background.image = to.image
        }
      })
    },
    onVideo() {
      SelectVideo(1).then((list) => {
        console.log(list)
        let { model } = this
        let media = list[0]
        let address = media.address
        model.address = address.mp4
        if (!model.background.image) model.background.image = media.cover_url
      })
    },
  },
}
