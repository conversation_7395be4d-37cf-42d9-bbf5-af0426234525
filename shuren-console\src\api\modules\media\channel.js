import request from '../request'
// import userRequest from '../userRequest'
const TypeList = [
  { key: 0, val: '路径' },
  { key: 1, val: '栏目' },
  { key: 2, val: '广告' },
]
const TypeKeys = TypeList.reduce((prev, curr) => {
  prev[curr.key] = curr.val
  return prev
}, {})

// function toTree(rows) {
//   let list = [],
//     map = {}

//   rows.forEach((item) => (map[item.id] = item))

//   rows.forEach((item) => {
//     let parent = map[item.pid]
//     if (parent) {
//       ;(parent.children || (parent.children = [])).push(item)
//     } else {
//       list.push(item)
//     }
//   })

//   return list
// }

export default {
  list(q) {
    return request.get('/v1/media.channel.list', q)
  },
  type(id) {
    return TypeKeys[id] || '未知'
  },
  get types() {
    return TypeList.slice()
  },
  create(data) {
    return request.post('/v1/media.channel.create', data)
  },
  update(data) {
    return request.post('/v1/media.channel.update', data)
  },
  delete(id) {
    return request.post('/v1/media.channel.delete', { id: id })
  },
  // delete(id) {
  //   return request.post('/v1/media.channel.delete', { id: id })
  // },
  treeList(query) {
    return request.get('/v1/user.account.treeData', query)
  },
  unassignList() {
    return request.get('/v1/user.account.unassign')
  },
  // 获取通知
  getNotice(query) {
    return request.get('/v1/sys.notice.query', query)
  },
  delNotice(id) {
    return request.post('/v1/sys.notice.delete', { id: id })
  },
  // 获取轮播素材
  getSwipe() {
    return request.get('/v1/home.carousel.query')
  },

  // 获取活动及通知列表
  getActivityList(query){
    return request.get('/v1/media.activity.list',query)
  },
  // 获取志愿者申请列表
  getAllList(query){
    return request.get('/v1/mini.volunteers.listAll',query)
  },
  getSignUpList(query){
    return request.get('/v1/media.activity.userSignUpList',query)
  }
  // // 获取志愿者申请列表
  // getVolunteersChildList(query){
  //   return request.get('/v1/mini.volunteers.child.list',query)
  // }
}
