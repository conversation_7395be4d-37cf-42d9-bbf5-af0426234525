<template>
  <div class="sidebar-header" @click="dialog=true">
    <el-avatar :size="40" :src="headimg"></el-avatar>
    <div class="sidebar-nickname ellipsis">{{nickname}}</div>
    <el-dialog :visible.sync="dialog" top="" :show-close="false" width="480px" custom-class="sidebar-avatar" :append-to-body="true">
      <div class="clearfix actions">
        <el-button plain type="primary" @click="modifyPwd">修改密码</el-button>
        <el-button plain type="primary" @click="uploadHeadImg">修改头像</el-button>
        <el-button plain type="primary" @click="loginOut">退出登录</el-button>
      </div>
    </el-dialog>

    <password :visible.sync="password"></password>
  </div>
</template>

<script>
  import UploadImage from '@/views/media/image/select';
  import Password from '@/layout/components/password';

  export default {
    components: {Password},
    data() {
      return {
        dialog: false,
        password: false
      }
    },
    computed: {
      headimg() {
        return this.$store.state.admin.headimg || 'https://cube.elemecdn.com/3/7c/********************************.png';
      },
      nickname() {
        return this.$store.state.admin.nickname;
      }
    },
    methods: {
      close() {
        this.$nextTick(_ => this.dialog = false);
      },
      uploadHeadImg() {
        UploadImage(1).then(list => {
          let {url} = list[0];
          this.dialog = false;

          return this.$store.dispatch('admin/headimg', url);
        }).then(_ => {
          this.$message.success('修改头像成功！');
        });
        this.close();
      },
      loginOut() {
        this.$store.dispatch('admin/logout');
      },
      modifyPwd() {
        this.password = true;
        this.close();
      }
    }
  }
</script>