<template>
  <div class="user-account-page">
    <mytable ref="table" api="/v1/user.account.query" :query="search">
      <el-form class="search-form" slot="toolbar" size="small" label-width="88px">
        <div class="search-item">
          <el-form-item label="注册时间">
            <el-date-picker
                v-model="created"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="请选择"
                end-placeholder="请选择"
                value-format="timestamp"
                align="center"
                :default-time="['00:00:00', '23:59:59']"
                @change="onCreatedChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="注册用户">
            <el-input placeholder="请输入" v-model="userVal" class="input-with-select">
              <el-select v-model="userKey" slot="prepend" placeholder="请选择">
                <el-option label="ID" value="id"></el-option>
                <el-option label="手机号" value="mobile"></el-option>
                <el-option label="邀请码" value="invcode"></el-option>
                <el-option label="登录账号" value="username"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item label="最后关注">
            <el-date-picker
                v-model="subscribed"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="请选择"
                end-placeholder="请选择"
                value-format="timestamp"
                align="center"
                :default-time="['00:00:00', '23:59:59']"
                @change="onSubChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="推荐人的">
            <el-input placeholder="请输入" v-model="parentVal" class="input-with-select">
              <el-select v-model="parentKey" slot="prepend" placeholder="请选择">
                <el-option label="ID" value="pid"></el-option>
                <el-option label="手机号" value="p_mobile"></el-option>
                <el-option label="邀请码" value="p_invcode"></el-option>
                <el-option label="登录账号" value="p_username"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item label="所在区域">
            <el-address :province.sync="search.province_code" :city.sync="search.city_code" :level="2"></el-address>
          </el-form-item>
          <el-form-item label="实名认证">
            <el-select v-model="search.realname_status" placeholder="全部状态" clearable>
              <el-option label="未申请" value="0"></el-option>
              <el-option label="待审核" value="1"></el-option>
              <el-option label="已通过" value="2"></el-option>
              <el-option label="被驳回" value="3"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item>
            <el-button @click="addUser()" size="small" title="添加新用户">创建</el-button>
            <el-button size="small" @click.stop.prevent="resetSearch" title="重置查询条件，以便新的搜索">重置</el-button>
            <el-button type="primary" size="small" native-type="submit">查询</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table-column prop="id" label="ID" width="75" align="center" fixed="left"></el-table-column>
      <el-table-column prop="nickname" label="昵称"></el-table-column>
      <el-table-column prop="mobile" label="手机号" align="center" width="100"></el-table-column>
      <el-table-column prop="username" label="登录账号" align="center" width="150"></el-table-column>
      <el-table-column prop="email" label="邮箱账号" align="center" width="130"></el-table-column>
      <el-table-column prop="last_login" label="最后登录" align="center" width="110">
        <template slot-scope="scope">{{ getLastLogin(scope.row.last_login) }}</template>
      </el-table-column>
      <el-table-column prop="issub" label="关注服务号" align="center">
        <template slot-scope="scope">{{ getSubWxmp(scope.row) }}</template>
      </el-table-column>
      <el-table-column prop="name" label="实名认证" align="center">
        <div class="realname" slot-scope="scope" @click.stop="openRealname(scope.row.id)">{{ getRealname(scope.row) }}</div>
      </el-table-column>
      <el-table-column prop="sex" label="性别" align="center">
        <template slot-scope="scope">{{ getSex(scope.row.sex) }}</template>
      </el-table-column>
      <el-table-column prop="city_name" label="所在区域"></el-table-column>
      <el-table-column label="操作" width="60" align="center" class-name="table-action" fixed="right">
        <div slot-scope="scope" class="action">
          <el-dropdown @command="command($event, scope.row)">
            <span class="el-dropdown-link">
               <el-button class="btn-action" size="mini">
                 <svg class="icon" aria-hidden="true">
                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                  </svg>
               </el-button>
            </span>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">编辑账号</el-dropdown-item>
              <el-dropdown-item command="permission">角色权限</el-dropdown-item>
              <el-dropdown-item command="wallet">账户余额</el-dropdown-item>
              <el-dropdown-item command="unmobile" v-if="!!scope.row.mobile">解绑手机</el-dropdown-item>
              <el-dropdown-item command="password">登录密码</el-dropdown-item>
              <!--<el-dropdown-item command="children">推荐关系</el-dropdown-item>-->
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-table-column>
    </mytable>
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<script>

import ElAddress from "@/components/address/select";

function getTimeRange(time) {
  let start = new Date(), end = new Date();

  start.setHours(0);
  start.setMinutes(0);
  start.setSeconds(0);
  start.setTime(start.getTime() - time * ********);

  end.setHours(23);
  end.setMinutes(59);
  end.setSeconds(59);

  return [start, end];
}

export default {
  name: 'UserAccountPage',
  components: {ElAddress},
  data() {
    return {
      userKey: 'mobile',
      userVal: '',
      parentKey: 'p_invcode',
      parentVal: '',
      search: {
        realname_status: '',
        province_code: 0,
        city_code: 0
      },
      created: null,
      subscribed: null,
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', getTimeRange(0));
          }
        }, {
          text: '昨天',
          onClick(picker) {
            let start = new Date(), end = new Date();

            start.setHours(0);
            start.setMinutes(0);
            start.setSeconds(0);
            start.setTime(start.getTime() - ********);

            end.setHours(23);
            end.setMinutes(59);
            end.setSeconds(59);
            end.setTime(end.getTime() - ********);

            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三天',
          onClick(picker) {
            picker.$emit('pick', getTimeRange(3));
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            picker.$emit('pick', getTimeRange(7));
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            picker.$emit('pick', getTimeRange(30));
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            picker.$emit('pick', getTimeRange(90));
          }
        }]
      },
      showEdit: false,
      editing: 0,
      showPermission: false
    }
  },
  watch: {
    userKey: {
      immediate: true,
      handler(v) {
        let {search} = this;
        ['id', 'username', 'mobile', 'code'].forEach(key => {
          if (key == v) {
            search[v] = this.userVal;
          } else {
            delete search[key];
          }
        });
      }
    },
    userVal: {
      immediate: true,
      handler(v) {
        this.search[this.userKey] = v;
      }
    },
    parentKey: {
      immediate: true,
      handler(v) {
        let {search} = this;
        ['pid', 'p_username', 'p_mobile', 'p_invcode'].forEach(key => {
          delete search[key];
        });
        search[v] = this.parentVal;
      }
    },
    parentVal: {
      immediate: true,
      handler(v) {
        this.search[this.parentKey] = v;
      }
    }
  },
  methods: {
    onCreatedChange(v) {
      this.search.created = v && parseInt(v[0] / 1000) + '-' + parseInt(v[1] / 1000);
    },
    onSubChange(v) {
      this.search.subscribed = v && parseInt(v[0] / 1000) + '-' + parseInt(v[1] / 1000);
    },
    resetSearch() {
      this.search = {
        realname_status: '',
        province_code: 0,
        city_code: 0
      };
    },
    refresh() {
      this.$refs.table.refresh();
    },
    getSex(val) {
      switch (val) {
        case 1:
          return '男';
        case 2:
          return '女';
        default:
          return '-';
      }
    },
    getRealname(item) {
      switch (item.realname_status) {
        case 0:
          return '未认证';
        case 1:
          return '待审核';
        case 2:
          return item.name;
        case 3:
          return '未通过';
        default:
          return '异常';
      }
    },
    getLastLogin(time) {
      return time ? this.$utils.Date(time).format('YYYY年MM月DD日') : '-';
    },
    getSubWxmp(item) {
      switch (item.issub) {
        case 0:
          return '未关注';
        case 1:
          return this.$utils.Date(item.last_sub).format('YYYY年MM月DD日');
        default:
          return '-';
      }
    },
    openRealname(id) {
      Realname(id).then(this.refresh);
    },
    unmobile(user) {
      this.$confirm('操作不可恢复，确定解绑【' + user.mobile + '】吗?', '提示', {
        confirmButtonText: '解绑',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(_ => {
        return this.$api.user.account.unbind(user.id, 'mobile', user.mobile);
      }).then(_ => {
        user.mobile = '';
      });
    },
    command(e, row) {
      switch (e) {
        case 'edit':
          return this.editUser(row.id);
        case 'password':
          return Password(row.id);
        case 'unmobile':
          return this.unmobile(row);
        case 'children':
          return this.$refs.table.refresh(this.resetSearch({parent_key: 'id', parent_val: row.id}));
        case 'wallet':
          return WalletAccount(row.id);
        case 'permission':
          return this.editPermission(row.id);
      }
    },
    editUser(id) {
      this.editing = id;
      this.showEdit = true;
    },
    editPermission(id) {
      this.editing = id;
      this.showPermission = true;
    }
  }
}
</script>