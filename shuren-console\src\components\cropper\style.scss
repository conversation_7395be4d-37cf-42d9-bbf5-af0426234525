.cropper-container {
  z-index: 1;
  position: absolute;
  left: 15px;
  right: 15px;
  top: 15px;
  bottom: 15px;
  touch-action: none;
  pointer-events: none;

  .cropper-view{
    border: 1px dashed #409EFF;
    position: absolute;
  }

  .icon.drag {
    position: absolute;
    display: flex;
    pointer-events: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: move;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.5);
  }

  .handle {
    box-sizing: border-box;
    position: absolute;
    width: 10px;
    height: 10px;
    background: #409EFF;
    touch-action: auto;
    pointer-events: all;
  }

  .handle-tl {
    top: -10px;
    left: -10px;
    cursor: nw-resize;
  }

  .handle-tm {
    top: -10px;
    left: 50%;
    margin-left: -5px;
    cursor: n-resize;
  }

  .handle-tr {
    top: -10px;
    right: -10px;
    cursor: ne-resize;
  }

  .handle-ml {
    top: 50%;
    margin-top: -5px;
    left: -10px;
    cursor: w-resize;
  }

  .handle-mr {
    top: 50%;
    margin-top: -5px;
    right: -10px;
    cursor: e-resize;
  }

  .handle-bl {
    bottom: -10px;
    left: -10px;
    cursor: sw-resize;
  }

  .handle-bm {
    bottom: -10px;
    left: 50%;
    margin-left: -5px;
    cursor: s-resize;
  }

  .handle-br {
    bottom: -10px;
    right: -10px;
    cursor: se-resize;
  }

  @media only screen and (max-width: 768px) {
    [class*="handle-"]:before {
      content: '';
      left: -10px;
      right: -10px;
      bottom: -10px;
      top: -10px;
      position: absolute;
    }
  }
}