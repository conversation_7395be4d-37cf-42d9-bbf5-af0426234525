<template>
  <div class="menu-item" :class="{ opened: opened }">
    <template v-if="isSub">
      <div class="menu-group" :style="padding" @click.stop.prevent="toggle">
        <svg aria-hidden="true" class="icon" v-if="level == 0 && menu.icon">
          <use :xlink:href="`#${menu.icon}`"></use>
        </svg>
        <span class="menu-name">{{ menu.title }}</span>
      </div>
      <div class="menu-list" ref="sub">
        <sidebar-item
          v-for="(item, index) in menu.children"
          :key="index"
          :menu="item"
          :level="level + 1"
          :active="active"
        ></sidebar-item>
      </div>
    </template>
    <div
      class="menu-title"
      :class="{ active: isActive }"
      :style="padding"
      v-else
      @click.stop.prevent="onClick"
    >
      <svg aria-hidden="true" class="icon" v-if="level == 0 && menu.icon">
        <use :xlink:href="`#${menu.icon}`"></use>
      </svg>
      <span class="menu-name">{{ menu.title }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "sidebar-item",
  props: ["menu", "level", "active"],
  data() {
    return {
      timer: 0,
      animate: 0,
      opened: 0,
    };
  },
  computed: {
    padding() {
      return "padding-left:" + (this.level * 20 + 20) + "px";
    },
    isSub() {
      let { children } = this.menu;
      return children && children.length > 0;
    },
    sidebar() {
      let $p = this.$parent;
      while ($p && $p.$options.name != "sidebar") {
        $p = $p.$parent;
      }
      return $p;
    },
    isActive() {
      return this.menu.id == this.active;
    },
    $sub() {
      return this.$refs.sub;
    },
  },
  watch: {
    isActive(v) {
      v && this.$parent.open(true);
    },
  },
  mounted() {
    this.isActive && this.$parent.open(true);
  },
  methods: {
    clear() {
      cancelAnimationFrame(this.animate);
      clearTimeout(this.timer);
    },
    handler(opened) {
      this.clear();

      let scope = this,
        el = this.$sub,
        height;
      scope.animate = requestAnimationFrame(function () {
        scope.opened = opened;

        if (opened) {
          el.style.cssText = "display:block;height:0";
          height = el.scrollHeight;
        } else {
          el.style.cssText = "display:block;height:" + el.scrollHeight + "px";
          height = 0;
        }

        scope.animate = requestAnimationFrame(function () {
          el.style.cssText =
            "transition:height .3s;display:block;height:" + height + "px";

          scope.timer = setTimeout(function () {
            el.style.cssText = opened ? "display:block" : "display:none";
          }, 350);
        });
      });
    },
    toggle() {
      this.handler(!this.opened);
    },
    open(all) {
      this.clear();
      this.opened = 1;
      this.$sub.style.cssText = "display:block";

      if (all) {
        let $p = this.$parent;
        if ($p.$options.name == "sidebar-item") {
          $p.open(all);
        }
      }
    },
    close() {
      this.clear();
      this.opened = 0;
      this.$sub.style.cssText = "display:none";
    },
    onClick() {
      this.sidebar.onClick(this.menu);
    },
  },
};
</script>