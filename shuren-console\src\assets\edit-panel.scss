.edit-panel-page {
  background: #f1f1f1 !important;
  display: flex;
}

.edit-panel-box {
  flex: 1;
  display: flex;
  max-width: 1100px;
  min-height: 100%;
  min-width: 900px;
  margin: 0 auto;
}

.edit-panel-side {
  width: 28%;
  min-width: 250px;
  padding: 30px 40px;
  background-color: #f8f9fc;
  border-right: 1px #e3e7ed solid;

  .upload-cover-image .is-empty {
    background: #f1f1f1;
  }
}

.edit-panel-name {
  font-size: 22px;
  font-weight: bold;
  color: #414a60;
}

.edit-panel-menu {
  margin-top: 40px;

  .item {
    cursor: pointer;
    display: flex;
    margin: 0 -40px;
    padding: 0 40px;
    height: 50px;
    line-height: 50px;
    color: #414a60;

    &:hover {
      color: #414a60;
      background-color: #f0f2f5;
    }

    &.active {
      background-color: #e3e7ed;
    }

    &.active:after {
      content: '';
      position: absolute;
      top: 50%;
      right: -7px;
      margin-top: -6px;
      width: 12px;
      height: 12px;
      background-color: #fff;
      transform: rotate(45deg);
    }
  }

  .label {
    flex: 1;
  }
}

.edit-panel-content {
  flex: 1;
  background-color: #fff;
  padding: 0 40px 40px 40px;
  overflow-x: hidden;
}

.edit-panel-title {
  color: #414a60;
  font-size: 18px;
  font-weight: bold;
  margin-top: 30px;
}

.edit-panel-desc {
  color: #8d92a1;
  font-size: 13px;
  margin-top: 10px;
  line-height: 18px;
}

.edit-panel-body {
  margin-top: 30px;
}

.edit-panel-action {
  text-align: right;
  margin-top: 30px;
}

.edit-panel-submit {
  text-align: center;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 15px;
}