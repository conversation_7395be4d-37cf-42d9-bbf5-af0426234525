<template>
  <div class="edit-panel-page edit-media-panel">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">音频素材</div>

        <media-image v-model="model.cover_url" style="margin-top:30px;width:250px;height:140px;"></media-image>

        <div class="edit-panel-menu">
          <div class="item" v-for="item in menu" :class="{active:active==item.name}" @click="go(item.name)">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <div class="edit-panel-submit">
          <el-button type="primary" size="small" @click="onPublish">发 布</el-button>
        </div>
      </div>
      <div class="edit-panel-content">
        <keep-alive>
          <component ref="part" v-bind:is="`edit-${active}`"></component>
        </keep-alive>
        <div class="edit-panel-action">
          <el-button size="small" @click="prev()" v-show="current>0">上一步</el-button>
          <el-button type="primary" size="small" v-show="current<menu.length-1" @click="next()">下一步</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Base from '../../publish/panel';
import MediaImage from '@/views/media/image/cover';
import EditBase from './base';
import EditChannel from './channel';
import EditContent from './content';

export default {
  extends: Base,
  components: {MediaImage, EditBase, EditChannel, EditContent},
  data() {
    return {
      menu: [
        {label: '基础信息', value: '', name: 'base'},
        {label: '展示位置', value: '', name: 'channel'},
        {label: '详细介绍', value: '选填', name: 'content'},
      ],
      model: this.value
    }
  }
}
</script>