<template>
  <div class="edit-panel-page edit-media-panel edit-media-motto">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">名言金句</div>

        <media-image v-model="model.cover_url" style="margin-top:30px;width:100%;height:140px;"></media-image>

        <div class="edit-panel-menu">
          <div class="item" v-for="item in menu" :class="{active:active==item.name}" @click="go(item.name)">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <div class="edit-panel-submit">
          <el-button type="primary" size="small" @click="onPublish">发 布</el-button>
        </div>
      </div>
      <div class="edit-panel-content">
        <keep-alive>
          <component ref="part" v-bind:is="`edit-${active}`"></component>
        </keep-alive>
        <div class="edit-panel-action">
          <el-button size="small" @click="prev()" v-show="current>0">上一步</el-button>
          <el-button type="primary" size="small" v-show="current<menu.length-1" @click="next()">下一步</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.media-motto-upbgmusic {
  .el-button {
    position: absolute;
    right: 2px;
    top: 2px;
    background: #fff;
    padding: 0 10px;
    bottom: 2px;
  }
}

.media-motto-item {
  margin-bottom: 8px;
  background: #f5f7fa;
  line-height: 40px;
  padding: 0 20px;
  font-size: 14px;
  cursor: move;
  user-select: none;

  .action {
    display: none;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #409eff;
  }

  &:hover .action {
    display: block;
  }
}

.motto-rotate-music {
  animation: music-circling 20s linear infinite;
}

@keyframes music-circling {
  from {
    transform: rotate(0deg)
  }

  to {
    transform: rotate(1turn)
  }
}

.edit-media-motto {
  .el-slider__marks {
    position: static;
  }

  .el-slider__marks-text {
    width: 30px;
    text-align: center;
  }
}

</style>

<script>
import Base from '../../publish/panel';
import MediaImage from '@/views/media/image/cover';
import EditBase from './base';
import EditChannel from './channel';
import EditContent from './content';
import EditBackgroundImage from './background-image';
import EditBackgroundColor from './background-color';
import EditBackgroundMusic from './background-music';

export default {
  extends: Base,
  components: {MediaImage, EditBase, EditChannel, EditContent, EditBackgroundImage, EditBackgroundColor, EditBackgroundMusic},
  data() {
    return {
      menu: [
        {label: '基础信息', value: '', name: 'base'},
        {label: '背景图片', value: '', name: 'background-image'},
        {label: '背景颜色', value: '', name: 'background-color'},
        {label: '背景音乐', value: '', name: 'background-music'},
        {label: '文字列表', value: '', name: 'content'},
        {label: '展示位置', value: '', name: 'channel'},
      ],
      model: this.value || {
        type: 'motto',
        status: 'editing',
        title: '',
        author: '',
        source: '',
        subtitle: '',
        abstract: '',
        cover_url: '',
        pubdate: null,
        interval: 3,
        content: [],
        bgcolor: 'rgba(0,0,0,1)',
        bgimage: '',
        bgmusic: {
          url: '',
          name: '',
          loop: 1,
          volume: 1,
          enabled: 0
        },
        channels: [],
        display_date: null,
        staff_id: null
      }
    }
  }
}
</script>