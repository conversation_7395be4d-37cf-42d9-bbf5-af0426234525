<template>
  <div>
    <mytable ref="table" api="/v1/region.info.query" :query="query" :list="list">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <el-input class="search-val" size="mini" v-model="query.code" placeholder="请输入编号" style="width: 200px"
                      maxlength="5" clearable>
            </el-input>
            <el-button size="mini" icon="el-icon-search" title="搜索" circle @click="searchFun"></el-button>
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>
      <el-table-column label="编号" prop="code" align="center"></el-table-column>
      <el-table-column label="网格名称" prop="parent_code">
        <template slot-scope="scope">
          <span v-show="scope.row.parent_code == 20">望凤街网格</span>
          <span v-show="scope.row.parent_code == 10">中兴街网格</span>
        </template>
      </el-table-column>
      <el-table-column label="小区名称" prop="name" align="center"></el-table-column>
      <el-table-column label="VR地址" prop="remark" align="center"></el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
    <el-dialog :title="flag ? '添加小区' : '编辑小区'" :visible.sync="dialogVisible" width="500px" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">

      <el-form ref="form" label-width="80px" :model="model">
        <el-form-item label="编号" prop="code" v-show="flag">
          <el-input v-model.trim="model.code" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="所属网格" prop="code">
          <!-- <el-input v-model.trim="model.parent_code" placeholder="必填项"></el-input> -->
          <el-radio v-model="model.parent_code" label="20">望凤街网格</el-radio>
          <el-radio v-model="model.parent_code" label="10">中兴街网格</el-radio>
        </el-form-item>
        <el-form-item label="小区名称" prop="name">
          <el-input v-model.trim="model.name" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model.number="model.sort" placeholder="必填项"
                    onkeypress='return( /[\d]/.test(String.fromCharCode(event.keyCode)))'></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="model.remark" placeholder="必填项"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/api/modules/request.js"
export default {
  name: 'grid',
  data() {
    return {
      list: [],
      flag: true,
      dialogVisible: false,
      model: {
        name: '',
        sort: 0,
        remark: '',
        code: '',
        type: '1',
        parent_code: ""
      },
      query: {
        name: '',
        code: '',
        type: '1',
        offset: 0,
        limit: 15
      }
    };
  },
  created() {
    // this.load()
  },
  mounted() {

  },
  methods: {
    searchFun() {
      request.get('/v1/region.info.query?name=' + this.query.name + '&&code=' + this.query.code + '&&type=1').then(res => {
        // console.log(res);
        this.list = res.rows
      })
    },
    refresh() {
      this.$refs.table.refresh();
    },
    onAdd() {
      this.flag = true
      this.model.id = 0
      this.model.code = ''
      this.model.name = ''
      this.model.sort = 0
      this.model.remark = ''
      this.model.parent_code = ''
      this.dialogVisible = true
    },
    onSubmit() {
      if (this.flag) {
        request.post('/v1/region.info.create', this.model).then(res => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        // 
        request.post('/v1/region.info.update', this.model).then(res => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.model = row
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.post('/v1/region.info.delete', { id: row.id }).then(res => {
          this.refresh()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  },
};
</script>

<style lang="scss" scoped></style>