<template>
  <el-select
      v-model="value"
      @change="onChange"
      filterable
      allow-create
      default-first-option
      placeholder="如：中国人民日报">
    <el-option v-for="(item, index) in list" :key="index" :label="item" :value="item"></el-option>
  </el-select>
</template>

<script>
const KEY = 'MediaSource';

export default {
  props: ['value'],
  data() {
    let cache = localStorage.getItem(KEY);
    return {
      list: cache ? JSON.parse(cache) : []
    }
  },
  methods: {
    onChange(str) {
      let json = localStorage.getItem(KEY);
      let list = json ? JSON.parse(json) : [];

      if (str) {
        str = str.replace(/\s/g, '');
        if (list.indexOf(str) == -1) list.splice(0, 0, str);
        localStorage.setItem(KEY, JSON.stringify(list));
      }

      this.$emit('input', str);
    }
  }
}
</script>