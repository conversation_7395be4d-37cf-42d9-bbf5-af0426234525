import Env from './env';
import require from './require';

const Utils = {};

// 深度拷贝
Utils.assign = function () {
  let i = 1, list = arguments, len = list.length, res = list[0], obj, key;

  for (; i < list.length; i++) {
    obj = list[i];
    for (key in obj) {
      if (typeof obj[key] == 'object' && !(obj[key] instanceof Array)) {
        if (typeof res[key] != 'object') {
          res[key] = {};
        }
        Utils.assign(res[key], obj[key]);
      } else {
        res[key] = obj[key];
      }
    }
  }

  return res;
};

// 秒转成时分
Utils.durationToTime = function (duration) {
  duration = parseInt(duration);

  let r = []
    , hour = parseInt(duration / 3600)
    , other = duration % 3600
    , min = parseInt(other / 60)
    , sec = parseInt(other % 60);

  if (hour > 0) r.push(hour < 10 ? '0' + hour : hour);
  r.push(min < 10 ? '0' + min : min);
  r.push(sec < 10 ? '0' + sec : sec);

  return r.join(':');
};

// 时分转成秒
Utils.timeToDuration = function (time) {
  let rows = time.split(':'), len = 3 - rows.length, i = 0;
  for (; i < len; i++) {
    rows.splice(0, 0, 0);
  }
  return rows[0] * 3600 + rows[1] * 60 + rows[2];
};

// 存储单位换算
Utils.bytesToSize = function (byte) {
  if (byte < 1) return '0';
  let k = 1024,
    sizes = ['字节', 'KB', 'M', 'G', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(byte) / Math.log(k));

  return parseFloat((byte / Math.pow(k, i)).toPrecision(3)) + sizes[i];
}

// 格式化数字
Utils.formatNumber = function (num, max = 100) {
  num = /^\d+$/.test(num) ? parseInt(num) : 0;
  if (num <= max) return num;
  if (num < 1000) return parseInt(num / 100) + '00+';
  if (num < 10000) return parseFloat((num / 1000).toFixed(1)) + 'k';
  return parseFloat((num / 10000).toFixed(1)) + '万';
};

// 倒计时
Utils.daojishi = function (endTime, startTime) {
  if (!startTime) startTime = Date.now();
  let leftTime = endTime - startTime
    , leftsecond = parseInt(leftTime / 1000)
    , day = Math.floor(leftsecond / (60 * 60 * 24))
    , hour = Math.floor((leftsecond - day * 24 * 60 * 60) / 3600)
    , minute = Math.floor((leftsecond - day * 24 * 60 * 60 - hour * 3600) / 60)
    , second = Math.floor(leftsecond - day * 24 * 60 * 60 - hour * 3600 - minute * 60);
  return {day: day, hour: hour, minute: minute, second: second}
};

// 节流函数
Utils.throttle = function (fn, delay) {
  let last = 0, now, timer = 0;

  return function () {
    clearTimeout(timer);
    now = Date.now();

    if (now - last >= delay) {
      last = now;
      fn.apply(this, arguments);
    } else {
      let content = this, args = arguments;
      timer = setTimeout(function () {
        fn.apply(content, args);
      }, delay);
    }
  };
};

// 防抖函数
Utils.debounce = function (fn, delay) {
  let timer;

  return function () {
    clearTimeout(timer);
    let context = this, args = arguments;
    timer = setTimeout(function () {
      fn.apply(context, args);
    }, delay);
  }
};

// 格式化日期
Utils.Date = function (time) {
  let date;

  if (/^\d{9,10}$/.test(time)) {
    date = new Date(time * 1000);
  } else if (/^\d{13}$/.test(time)) {
    date = new Date(time);
  } else if (time instanceof Date) {
    date = time;
  } else {
    date = new Date();
  }

  return {
    get timestamp() {
      return parseInt(date.valueOf() / 1000);
    },
    get yyyy() {
      return this.y;
    },
    get yy() {
      return this.y.toString().substr(2);
    },
    get mm() {
      return this.m < 10 ? '0' + this.m : this.m;
    },
    get dd() {
      return this.d < 10 ? '0' + this.d : this.d;
    },
    get hh() {
      return this.h < 10 ? '0' + this.h : this.h;
    },
    get ii() {
      return this.i < 10 ? '0' + this.i : this.i;
    },
    get ss() {
      return this.s < 10 ? '0' + this.s : this.s;
    },
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    sss: date.getMilliseconds(),
    prev(day) {
      return Utils.Date(this.timestamp - (day || 1) * 86400);
    },
    next(day) {
      return Utils.Date(this.timestamp + (day || 1) * 86400);
    },
    toString: function (format) {
      return (format || 'YYYY-MM-DD HH:mm:ss').replace('sss', this.sss).replace('yy', this.yy).replace('YYYY', this.yyyy).replace('MM', this.mm).replace('DD', this.dd).replace('HH', this.hh).replace('mm', this.ii).replace('ss', this.ss);
    },
    toNumber: function () {
      return parseInt(date.valueOf() / 1000);
    },
    format: function (format) {
      let jt = Utils.Date(), diff = jt.timestamp - this.timestamp;

      if (diff < 0) return this.toString(format);
      if (diff < 60) return '刚刚';
      if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
      if (diff <= 86400) return Math.floor(diff / 3600) + '小时前';

      let zt = jt.prev();
      if (diff < 172800 && zt.d == this.d) {
        if (zt.d == this.d) return '昨天' + this.hh + ':' + this.ii;
      }

      if (diff < 259200) {
        let qt = zt.prev();
        if (qt.d == this.d) return '前天' + this.hh + ':' + this.ii;
      }

      format = format || 'YYYY-MM-DD HH:mm';

      if (jt.y == this.y) return this.toString(format.replace(/^YYYY(-|年)/, ''));

      return this.toString(format);
    }
  }
};

// 表情
Utils.emoji = (function () {
  let url = Env.protocol + '//res.wx.qq.com/mpres/htmledition/images/icon/emotion/'
    , url2 = url.replace(/\//g, '\\/')
    , emoji = new Array({v: '微笑', k: 0}, {v: '撇嘴', k: 1}, {v: '色', k: 2}, {v: '发呆', k: 3}, {v: '得意', k: 4},
    {v: '流泪', k: 5}, {v: '害羞', k: 6}, {v: '闭嘴', k: 7}, {v: '睡', k: 8}, {v: '大哭', k: 9},
    {v: '尴尬', k: 10}, {v: '发怒', k: 11}, {v: '调皮', k: 12}, {v: '呲牙', k: 13}, {v: '惊讶', k: 14},
    {v: '难过', k: 15}, {v: '酷', k: 16}, {v: '冷汗', k: 17}, {v: '抓狂', k: 18}, {v: '吐', k: 19},
    {v: '偷笑', k: 20}, {v: '可爱', k: 21}, {v: '白眼', k: 22}, {v: '傲慢', k: 23}, {v: '饥饿', k: 24},
    {v: '困', k: 25}, {v: '惊恐', k: 26}, {v: '流汗', k: 27}, {v: '憨笑', k: 28}, {v: '大兵', k: 29},
    {v: '奋斗', k: 30}, {v: '咒骂', k: 31}, {v: '疑问', k: 32}, {v: '嘘', k: 33}, {v: '晕', k: 34},
    {v: '折磨', k: 35}, {v: '衰', k: 36}, {v: '骷髅', k: 37}, {v: '敲打', k: 38}, {v: '再见', k: 39},
    {v: '擦汗', k: 40}, {v: '抠鼻', k: 41}, {v: '鼓掌', k: 42}, {v: '糗大了', k: 43}, {v: '坏笑', k: 44},
    {v: '左哼哼', k: 45}, {v: '右哼哼', k: 46}, {v: '哈欠', k: 47}, {v: '鄙视', k: 48}, {v: '委屈', k: 49},
    {v: '快哭了', k: 50}, {v: '阴险', k: 51}, {v: '亲亲', k: 52}, {v: '吓', k: 53}, {v: '可怜', k: 54},
    {v: '菜刀', k: 55}, {v: '西瓜', k: 56}, {v: '啤酒', k: 57}, {v: '篮球', k: 58}, {v: '乒乓', k: 59},
    {v: '咖啡', k: 60}, {v: '饭', k: 61}, {v: '猪头', k: 62}, {v: '玫瑰', k: 63}, {v: '凋谢', k: 64},
    {v: '示爱', k: 65}, {v: '爱心', k: 66}, {v: '心碎', k: 67}, {v: '蛋糕', k: 68}, {v: '闪电', k: 69},
    {v: '炸弹', k: 70}, {v: '刀', k: 71}, {v: '足球', k: 72}, {v: '瓢虫', k: 73}, {v: '便便', k: 74},
    {v: '月亮', k: 75}, {v: '太阳', k: 76}, {v: '礼物', k: 77}, {v: '拥抱', k: 78}, {v: '强', k: 79},
    {v: '弱', k: 80}, {v: '握手', k: 81}, {v: '胜利', k: 82}, {v: '抱拳', k: 83}, {v: '勾引', k: 84},
    {v: '拳头', k: 85}, {v: '差劲', k: 86}, {v: '爱你', k: 87}, {v: 'NO', k: 88}, {v: 'OK', k: 89},
    {v: '爱情', k: 90}, {v: '飞吻', k: 91}, {v: '跳跳', k: 92}, {v: '发抖', k: 93}, {v: '怄火', k: 94},
    {v: '转圈', k: 95}, {v: '磕头', k: 96}, {v: '回头', k: 97}, {v: '跳绳', k: 98}, {v: '挥手', k: 99},
    {v: '激动', k: 100}, {v: '街舞', k: 101}, {v: '献吻', k: 102}, {v: '左太极', k: 103}, {v: '右太极', k: 104});

  emoji.forEach(function (i) {
    i.encode = new RegExp('<img class="is-emoji" src="' + url2 + i.k + '\.gif">', 'gi');
    i.decode = new RegExp('\\[' + i.v + '\\]', 'gi');
    i.url = url + i.k + '.gif';
  });

  Object.defineProperties(emoji, {
    url: {
      value: 'https://res.wx.qq.com/mpres/htmledition/images/icon/emotion/'
    },
    encode: {
      value(str) {
        return emoji.forEach(function (i) {
          str = str.replace(i.encode, '[' + i.v + ']');
        }), str;
      }
    },
    decode: {
      value(str) {
        return emoji.forEach(function (i) {
          str = str.replace(i.decode, '<img class="is-emoji" src="' + url + i.k + '.gif">');
        }), str;
      }
    }
  });

  return emoji;
}());

// 将手机号带上+86
Utils.add86 = function (m) {
  return m ? (m.toString().substring(0, 1) == '+' ? m : '+86') + m : ''
};

// 获取当前地理位置
Utils.getPosition = function () {
  // 调试使用
  if (Env.debug) {
    return Promise.resolve({
      "lat": 39.80613,
      "lng": 116.296,
      "province_name": "北京市",
      "city_name": "市辖区",
      "district_name": "丰台区",
      "detail": "丰葆路709号吉艾花园",
      "address": "北京市丰台区花乡吉艾花园北京市大葆台西汉墓博物馆(装修中)",
      "province_id": 110000,
      "city_id": 110100,
      "district_id": 110106
    });
  }

  // 微信定位
  if (Env.IS_WEIXIN) return Utils.getWxLocation();

  // 支付宝定位
  if (Env.IS_ALIPAY) return Utils.getAliPayLocation();

  // APP定位
  if (Env.IS_APP) return Utils.getAppLocation();

  // 高德地图定位
  if (Env.IS_HTTPS) return Utils.getAmapLocation();

  return Promise.reject('不支持定位');
};

Utils.getAmapLocation = function () {
  return requirejs('amap').then(function () {
    let id = 'gaodemap' + Date.now(), node = document.createElement('div');
    node.setAttribute('id', id);
    node.setAttribute('style', "width:100%;height:100%;position:absolute;left:0;right:0;top:0;bottom:0;z-index:-1;opacity:0");
    document.body.appendChild(node);

    return new Promise(function (resolve, reject) {
      // 解析定位结果
      function onComplete(res) {
        node.remove();
        let addr = res.addressComponent;
        resolve({
          lat: res.position.lat,
          lng: res.position.lng,
          province_name: addr.province,
          city_name: addr.city || '市辖区',
          district_name: addr.district,
          detail: addr.street + addr.streetNumber + addr.neighborhood,
          address: res.formattedAddress,
          province_id: parseInt(addr.adcode.substr(0, 2) + '0000'),
          city_id: parseInt(addr.adcode.substr(0, 2) + addr.citycode + '0'),
          district_id: parseInt(addr.adcode),
        });
      }

      // 解析定位错误信息
      function onError(data) {
        node.remove();
        reject('定位失败：' + data.message);
      }

      let map = new AMap.Map(id, {resizeEnable: true});

      AMap.plugin('AMap.Geolocation', function () {
        let geolocation = new AMap.Geolocation({
          enableHighAccuracy: true,//是否使用高精度定位，默认:true
          timeout: 10000,          //超过10秒后停止定位，默认：5s
          buttonPosition: 'RB',    //定位按钮的停靠位置
          buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
          zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点
        });
        map.addControl(geolocation);
        geolocation.getCurrentPosition(function (status, result) {
          status == 'complete' ? onComplete(result) : onError(result);
        });
      });
    });
  });
};

// 微信定位
Utils.getWxLocation = function () {
  return new Promise(function (resolve, reject) {
    global.wx.ready(function () {
      global.wx.getLocation({
        type: 'wgs84',
        success(res) {
          resolve({lat: res.latitude, lng: res.longitude});
        },
        cancel() {
          reject('微信定位失败');
        },
        fail() {
          reject('微信定位失败');
        }
      });
    });
  });
};

// 支付宝定位
Utils.getAliPayLocation = function () {
  return new Promise(function (resolve, reject) {
    global.onAlipayReady(function () {
      AlipayJSBridge.call('getCurrentLocation', {requestType: 2, bizType: 'dili'}, function (res) {
        if (res.error) return reject('定位失败：' + res.errorMessage);

        let addr = res.streetNumber, city = res.city || res.province;

        resolve({
          lat: res.latitude,
          lng: res.longitude,
          province_name: res.province,
          city_name: city,
          district_name: res.district,
          detail: addr.street + addr.number,
          address: res.province + (res.province == city ? '' : city) + res.district + addr.street + addr.number,
          province_id: parseInt(res.cityAdcode.substr(0, 2) + '0000'),
          city_id: parseInt(res.cityAdcode),
          district_id: parseInt(res.districtAdcode)
        });
      });
    });
  });
};

// APP定位
Utils.getAppLocation = function () {
  return new Promise(function (resolve, reject) {
    Utils.onApiReady(function (api) {
      function request() {
        api.getLocation(function (ret, err) {
          ret && ret.status ? resolve({lat: ret.latitude, lng: ret.longitude}) : reject('定位失败');
        });
      }

      let perm = {list: ['location']}, rets = api.hasPermission(perm);

      if (rets[0].granted) return request();

      api.requestPermission(perm, function (ret, err) {
        ret.list[0].granted ? request() : reject('定位授权失败');
      });
    });
  });
};

Utils.md5 = function (obj) {
  return CryptoJS.MD5(obj).toString();
};

Utils.base64Encode = function (obj) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(typeof obj == 'string' ? obj : JSON.stringify(obj)))
};

Utils.base64Decode = function (str) {
  let json = CryptoJS.enc.Base64.parse(str).toString(CryptoJS.enc.Utf8);
  try {
    return JSON.parse(json)
  } catch (e) {
    return json
  }
};

Utils.base64ToImage = function (base64) {
  let arr = base64.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], 'base64.' + mime.split('/')[1], {type: mime});
};

Utils.pad = function (str, len) {
  str += '';
  while (str.length < len) {
    str = '0' + str;
  }
  return str;
};

/**************
 * ID加密/解密
 * type：11-99
 * pad：填充0，保证加密后长度
 ****************/
(function () {
  let options = {
    user: {
      pad: 6,
      key: '7W5AXD9NJFL61U3HCPQB2ZKORITS0E4V8MYG',
      type: {user: '11'}
    },
    media: {
      pad: 6,
      key: 'bpdfDqrnh4KzaNmCYA2HLiMsXw6y5JGkT98QUvjlgSotuWVB7F3ZPcRIOE01ex',
      type: {news: '12', video: '13', audio: '14', motto: '15'}
    }
  };

  function encode(id, key) {
    let num = new Number(id), str = '', mod, len = key.length;
    while (num) {
      mod = num % len;
      num = (num - mod) / len;
      str += key[mod];
    }
    return str;
  }

  function decode(str, key) {
    let id = 0, max = str.length, i = 0, len = key.length, o;
    for (; i < max; i++) {
      o = key.indexOf(str[i]);
      if (o == -1) return;
      id += o * Math.pow(len, i);
    }
    return id;
  }

  function encodeId(id, type) {
    let arr = type.split('.')
      , opt = options[arr[0]]
      , pre = opt.type[arr[1] || arr[0]]
      , zs = Utils.pad(parseInt(id / pre), opt.pad)
      , ys = Utils.pad(id % pre, 2)
      , str = pre[0] + ys[1] + pre[1] + zs + ys[0];
    return encode(str, opt.key);
  }

  function decodeId(str, group) {
    let opt = options[group]
      , res = decode(str, opt.key);
    if (!res) return;

    res = res.toString();

    let pre = res[0] + res[2]
      , yu = res.substr(-1) + res[1]
      , zs = res.substr(3, res.length - 3 - 1)
      , i;

    if (zs.length < opt.pad) return;

    for (i in opt.type) {
      if (opt.type[i] == pre) {
        return {id: zs * pre + parseInt(yu), type: i};
      }
    }
  }

  Utils.encodeId = encodeId;
  Utils.decodeId = decodeId;
}());

// 向左侧填充
Utils.pad = function (v, l) {
  v += '';
  while (v.length < l) v = '0' + v;
  return v;
};

// 移除场景
Utils.removepano = function (s) {
  let t, n, r, i, e;
  if (s) {
    e = s.id;
    t = window._krpMW;
    if (t) for (n = 0; n < t.length; n++) {
      r = t[n];
      if (r && r.id === e) {
        t.splice(n, 1);
        break
      }
    }
    s.unload && s.unload(), i = s.parentNode, i && i.removeChild(s)
  }
};

Utils.scrollTo = function () {
  let cubic = value => Math.pow(value, 3);
  let easeInOutCubic = value => value < 0.5 ? cubic(value * 2) / 2 : 1 - cubic((1 - value) * 2) / 2;

  return function (el, to) {
    let beginTime = Date.now();
    let beginValue = el.scrollTop;
    let rAF = window.requestAnimationFrame || (func => setTimeout(func, 16));
    let distance = to - beginValue;
    let frameFunc = () => {
      let progress = (Date.now() - beginTime) / 500;
      if (progress < 1) {
        el.scrollTop = beginValue + distance - distance * (1 - easeInOutCubic(progress));
        rAF(frameFunc);
      } else {
        el.scrollTop = to;
      }
    };
    rAF(frameFunc);
  }
}();

// 压缩图片
Utils.lrzimage = function () {
  function run(file, resolve, reject) {
    lrz(file, {width: 800}).then(rst => {
      if (!rst.file.name) {
        let name = rst.origin.name || Math.random().toString().substr(-3)
          , ext = rst.base64.match(/^data:image\/(\w+);/)[1];
        rst.file.name = name.substr(0, name.lastIndexOf('.')) + '.' + ext;
      }
      resolve(rst)
    }).catch(e => reject(e));
  }

  return function (files) {
    return require('lrz').then(function () {
      return new Promise((resolve, reject) => {
        if (files instanceof File) {
          return run(files, resolve, reject);
        }

        let len = files.length, list = [], lrz = function (i) {
          run(files[i], function (rst) {
            list.push(rst);

            if (++i < len) {
              lrz(i);
            } else {
              resolve(list);
            }
          }, reject);
        };

        lrz(0);
      });
    });
  }
}();

// 最大公约数
Utils.maxGongYue = function (m, n) {
  let u = +m, v = +n, t = v;
  while (v != 0) {
    t = u % v;
    u = v;
    v = t;
  }
  return u;
};


Utils.random = function (min, max) {
  return min + Math.round(Math.random() * (max - min));
};

Utils.isEmpty = function (val) {
  return val === undefined || val === null || val.toString() === '';
}

Utils.sort = function (obj) {
  if (!obj) return obj;

  let val, arr = [];

  if (obj instanceof Array) {
    obj.forEach(obj => {
      val = Utils.sort(obj);
      Utils.isEmpty(val) || arr.push(val);
    });
    return arr;
  }

  if (typeof obj != 'object') return obj;

  let model = {}, len = 0, res = {};
  for (let field in obj) {
    val = Utils.sort(obj[field]);
    if (!Utils.isEmpty(val)) {
      arr[len++] = field;
      model[field] = val;
    }
  }

  if (len == 0) return;

  arr.sort().forEach(field => {
    res[field] = model[field];
  });

  return res;
};

// 最大公约数
Utils.maxGongYue = function (m, n) {
  let u = +m, v = +n, t = v;
  while (v != 0) {
    t = u % v;
    u = v;
    v = t;
  }
  return u;
};

Utils.pickerOptions = function () {
  function getTimeRange(time) {
    let start = new Date(), end = new Date();

    start.setHours(0);
    start.setMinutes(0);
    start.setSeconds(0);
    start.setTime(start.getTime() - time * 86400000);

    end.setHours(23);
    end.setMinutes(59);
    end.setSeconds(59);

    return [start, end];
  }

  return function () {
    return {
      shortcuts: [{
        text: '今天',
        onClick(picker) {
          picker.$emit('pick', getTimeRange(0));
        }
      }, {
        text: '昨天',
        onClick(picker) {
          let start = new Date(), end = new Date();

          start.setHours(0);
          start.setMinutes(0);
          start.setSeconds(0);
          start.setTime(start.getTime() - 86400000);

          end.setHours(23);
          end.setMinutes(59);
          end.setSeconds(59);
          end.setTime(end.getTime() - 86400000);

          picker.$emit('pick', [start, end]);
        }
      }, {
        text: '最近三天',
        onClick(picker) {
          picker.$emit('pick', getTimeRange(3));
        }
      }, {
        text: '最近一周',
        onClick(picker) {
          picker.$emit('pick', getTimeRange(7));
        }
      }, {
        text: '最近一个月',
        onClick(picker) {
          picker.$emit('pick', getTimeRange(30));
        }
      }, {
        text: '最近三个月',
        onClick(picker) {
          picker.$emit('pick', getTimeRange(90));
        }
      }]
    }
  }
}();

Utils.objToMd5 = function (model) {
  model = Utils.sort(model);
  model = JSON.stringify(model);
  return CryptoJS.MD5(model).toString();
}

Utils.zhDate = function (time) {
  if (!time || time == '0000-00-00') return '';

  return time.substr(0, 4) + '年' + time.substr(5, 2) + '月' + time.substr(8, 2) + '日'
};

export default Utils;