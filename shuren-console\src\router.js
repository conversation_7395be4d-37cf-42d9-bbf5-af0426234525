import Layout from '@/layout'

const admin = [
  {
    path: '/',
    redirect: { name: 'media.all' },
  },
  {
    path: 'login',
    name: 'login',
    component: () => import('@/views/login'),
    meta: { title: '登录', layout: 0 },
  },
  {
    path: 'register',
    name: 'register',
    component: () => import('@/views/register'),
    meta: { title: '注册', layout: 0 },
  },
  {
    path: 'dashboard',
    name: 'dashboard',
    component: () => import('@/views/dashboard'),
    meta: { title: '工作台' },
  },
  {
    path: 'menu',
    name: 'menu',
    component: () => import('@/views/menu'),
    meta: { title: '菜单管理' },
  },
  {
    path: 'role',
    name: 'role',
    component: () => import('@/views/role'),
    meta: { title: '角色管理' },
  },
  {
    path: 'jurisdiction',
    name: 'jurisdiction',
    component: () => import('@/views/jurisdiction'),
    meta: { title: '层级管理' },
  },
  {
    path: 'allUser',
    name: 'allUser',
    component: () => import('@/views/allUser'),
    meta: { title: '用户管理' },
  },
  {
    path: 'homePage',
    name: 'homePage',
    component: () => import('@/views/homePage'),
    meta: { title: '用户审核' },
  },
  {
    path: 'company/setting',
    name: 'company.setting',
    component: () => import('@/views/company/setting'),
    meta: { title: '信息完善' },
  },
  {
    path: 'department',
    name: 'department',
    component: () => import('@/views/department'),
    meta: { title: '组织架构' },
  },
  {
    path: 'staff',
    name: 'staff',
    component: () => import('@/views/staff'),
    meta: { title: '成员管理' },
  },
  {
    path: 'staff/add/:company(\\d+)',
    name: 'staff.add',
    component: () => import('@/views/staff/edit'),
    meta: { title: '添加成员' },
  },
  {
    path: 'staff/edit/:company(\\d+)/:user(\\d+)',
    name: 'staff.edit',
    component: () => import('@/views/staff/edit'),
    meta: { title: '编辑成员' },
  },
  {
    path: 'staff/tag',
    name: 'staff.tag',
    component: () => import('@/views/staff/tag'),
    meta: { title: '分组管理' },
  },
  {
    path: 'media/all',
    name: 'media.all',
    component: () => import('@/views/media/all'),
    meta: { title: '素材管理' },
  },
  {
    path: 'media/notice',
    name: 'media.notice',
    component: () => import('@/views/media/notice'),
    meta: { title: '通知管理' },
  },
  {
    path: 'media/swipe',
    name: 'media.swipe',
    component: () => import('@/views/media/swipe'),
    meta: { title: '轮播管理' },
  },
  {
    path: 'media/publish',
    name: 'media.publish',
    component: () => import('@/views/media/publish/type'),
    meta: { title: '发布资源' },
  },
  {
    path: 'media/publish/:type(news|video|audio|motto|image)',
    name: 'media.editor',
    component: () => import('@/views/media/publish/editor'),
    meta: { title: '发布资源' },
  },
  {
    path: 'media/channel',
    name: 'media.channel',
    component: () => import('@/views/media/channel'),
    meta: { title: '频道管理' },
  },
  {
    path: 'about/contact/us',
    name: 'about.contact.us',
    component: () => import('@/views/about/contact/us'),
    meta: { title: '联系我们' },
  },
  {
    path: 'dictionary',
    name: 'dictionary',
    component: () => import('@/views/dictionary'),
    meta: { title: '数据字典' },
  },
  {
    path: 'app/home/<USER>',
    name: 'app.home.page',
    component: () => import('@/views/app/home/<USER>'),
    meta: { title: '首页设置' },
  },
  {
    path: 'app/remote/control',
    name: 'app.remote.control',
    component: () => import('@/views/app/remote/control'),
    meta: { title: '远程控制' },
  },
  {
    path: 'user/account/all',
    name: 'user.account.all',
    component: () => import('@/views/user/account/all'),
    meta: { title: '用户管理' },
  },
  {
    path: 'user/process',
    name: 'user.process',
    component: () => import('@/views/process'),
    meta: { title: '审核管理' },
  },
  {
    path: '/bigdata/grid',
    name: 'bigdata.grid',
    component: () => import('@/views/bigdata/grid'),
    meta: { title: '网格信息' },
  },
  {
    path: '/bigdata/community',
    name: 'bigdata.community',
    component: () => import('@/views/bigdata/community'),
    meta: { title: '小区信息' },
  },
  {
    path: '/bigdata/resident',
    name: 'bigdata.resident',
    component: () => import('@/views/bigdata/resident'),
    meta: { title: '居民信息' },
  },
  {
    path: '/user/honour',
    name: 'user.honour',
    component: () => import('@/views/honour'),
    meta: { title: '支部荣誉' },
  },
  {
    path: '/user/devparty',
    name: 'user.devparty',
    component: () => import('@/views/devparty'),
    meta: { title: '发展党员' },
  },
  {
    path: '/volunteer',
    name: 'volunteer',
    component: () => import('@/views/volunteer'),
    meta: { title: '志愿服务' },
  },
  {
    path: '/bigdata/economic',
    name: 'bigdata.economic',
    component: () => import('@/views/bigdata/economic'),
    meta: { title: '经济结构' },
  },
  {
    path: '/activity',
    name: 'activity',
    component: () => import('@/views/media/activity'),
    meta: { title: '活动及通知' },
  },
  {
    path: '/activity/people_list',
    name: 'activity.people_list',
    component: () => import('@/views/media/activity/people_list'),
    meta: { title: '报名列表' },
  },
  {
    path: '/volunteer_audit',
    name: 'volunteer_audit',
    component: () => import('@/views/media/volunteer'),
    meta: { title: '志愿者审核' },
  },
  {
    path: '/volunteer/list',
    name: 'volunteer_list',
    component: () => import('@/views/media/volunteer/list'),
    meta: { title: '志愿者列表' },
  },
  {
    path: '/integral',
    name: 'integral',
    component: () => import('@/views/media/integral/index'),
    meta: { title: '积分兑换列表' },
  },
  {
    path: '/skill',
    name: 'skill',
    component: () => import('@/views/media/skill/index'),
    meta: { title: '技能培训' },
  },
  {
    path: '/skill/list',
    name: 'skill.list',
    component: () => import('@/views/media/skill/list'),
    meta: { title: '技能培训报名表' },
  },
  {
    path: '/children',
    name: 'children',
    component: () => import('@/views/media/children/index'),
    meta: { title: '公益课堂报名表' },
  },
  {
    path: '/children/edit',
    name: 'children.edit',
    component: () => import('@/views/media/children/edit'),
    meta: { title: '公益课堂审核表' },
  },
  {
    path: '/soldier',
    name: 'soldier',
    component: () => import('@/views/media/soldier'),
    meta: { title: '军人信息登记列表' },
  },
  {
    path: '/passbook',
    name: 'passbook',
    component: () => import('@/views/media/passbook'),
    meta: { title: '志愿纪实列表' },
  },
  {
    path: '/passbook/review',
    name: 'passbook.review',
    component: () => import('@/views/media/passbook/review'),
    meta: { title: '志愿纪实审核' },
  },
]

// Vue.use(VueRouter);

export default new VueRouter({
  base: '/',
  mode: 'history',
  routes: [
    {
      path: '/',
      component: Layout,
      children: admin,
    },
  ],
})
