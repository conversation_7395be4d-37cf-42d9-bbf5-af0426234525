<template>
  <div>
    <div class="media-table-container">
      <mytable
        ref="table"
        :list="list"
        :api="$api.media.tierList"
        :query="userQuery"
      >
        <el-form class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <el-input
                class="search-val"
                size="mini"
                v-model="userQuery.nickname"
                placeholder="请输入"
                style="width: 100px"
                maxlength="5"
                clearable
              >
              </el-input>
              <el-button
                size="mini"
                icon="el-icon-search"
                title="搜索"
                circle
                @click="searchFun"
              ></el-button>
            </div>
          </div>
        </el-form>

        <el-table-column
          prop="id"
          label="编号"
          min-width="8%"
          align="center"
          :formatter="formatter"
        ></el-table-column>
        <el-table-column label="图标" min-width="5%" align="center">
          <template slot-scope="scope">
            <div :style="{ textAlign: 'center' }">
              <img
                :src="scope.row.logo"
                alt=""
                class="logo"
                v-if="scope.row.logo"
              />
              <p v-else>-----</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="用户昵称"
          prop="nickname"
          min-width="20%"
          align="center"
        ></el-table-column>
        <el-table-column
          label="单位全称"
          prop="c_name"
          min-width="20%"
          align="center"
        ></el-table-column>
        <el-table-column
          label="简称"
          prop="alias"
          min-width="16%"
          align="center"
        ></el-table-column>
        <el-table-column
          label="登陆账号"
          prop="username"
          min-width="16%"
          align="center"
        ></el-table-column>

        <el-table-column label="操作" align="center" min-width="10%">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" @click="edit(scope.row)" title="编辑">
                <svg class="icon" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xlink:href="#edit"
                  ></use>
                </svg>
              </el-button>
              <el-button class="btn-action" size="mini" @click="open(scope.row)" title="删除">
                <svg class="icon" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xlink:href="#delete"
                  ></use>
                </svg>
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>
    </div>

    <el-dialog title="编辑用户信息" :visible.sync="editDialog" :close-on-click-modal="false" width="30%">
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="昵称：">
            <el-input v-model="form.nickname" ></el-input>
          </el-form-item>
          <el-form-item label="全称：">
            <el-input v-model="form.c_name" ></el-input>
          </el-form-item>
          <el-form-item label="简称：">
            <el-input v-model="form.alias" ></el-input>
          </el-form-item>
          <el-form-item label="图标：">
              <div class="img-item" v-show="hasPic">
                <img :src="`${CDN_URL}/sys/upimg.png`" />
                <input
                  type="file"
                  class="file"
                  :disabled="upping"
                  v-on:change="fileChanged(-1)"
                  accept="image/jpeg,image/png,image/bmp"
                  multiple="multiple"
                />
                <div>建议尺寸不超过800像素，文件大小不超过300KB</div>
              </div>
              <div v-show="!hasPic" class="img-item2">
                  <img :src="picSrc" alt="" class="imgTu">
                  <div class="btns">
                    <el-button size="small" type="primary" @click="successUpload">{{fileList[0] ? fileList[0].message : 上传图标}}</el-button>
                    <el-button size="small"  @click="cancelUpload">取消上传</el-button>
                  </div>
              </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="marginLeft:100px;" @click="submitForm">确认修改</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>
import MediaBase from "../media/all/base";
import MediaChannel from "../media/channel/components/tree.vue";
import request from '@/utils/request';
export default {
  name: "allUser",
  extends: MediaBase,
  components: { MediaChannel },
  data() {
    return {
      multipleSelection: [],
      name: "",
      editDialog:false,
      // defalutLogoUrl: 'this.src="' + require("@/assets/errorImg.png") + '"',
      form:{
        nickname:"",
        company_id:0,
        c_name:"",
        alias:"",
        logo:""
      },
      upping:false,
      hasPic:true,
      picSrc:"",
      fileList:[],
      status: {
        1: "点击上传",
        2: "上传中",
        3: "已上传",
        4: "失　败",
      },
    };
  },
  computed: {
    list() {
      return [];
    },
  },
  watch: {
    // "query.channel"() {
    //   this.$refs.table.refresh(true);
    // },

  },
  methods: {
    // formatter(row) {
    //   row._type = this.$api.media.type[row.type];
    //   row._status = this.$api.media.status[row.status];
    //   row._pubdate = this.$utils.Date(row.pubdate).format();
    //   return row.id;
    // },
    refresh() {
      this.$refs.table.refresh();
      this.needRefresh = 0;
    },
    onCreated() {},
    onSubmit() {},
    selectFun(event) {
      this.multipleSelection = event;
    },
    edit(row){
      this.form = row
      this.editDialog = true
    },
    submitForm() {
        this.hasPic = true
        this.picSrc = ""
        request.post('v1/user.account.update',this.form).then(()=>{
          this.$message({
            type: 'success',
            message: '修改成功!'
          });
          this.editDialog = false
          this.refresh()
        })
    },
    open(row) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.post('v1/user.account.delete',{id:row.id}).then(()=>{
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.refresh()
        })
      })
    },
    fileChanged(index) {
      
      this.hasPic = false
      let ele = event.target || event.srcElement;
      console.log(ele.files);
      for (let i = 0, len = ele.files.length; i < len; i++) {
        let file = ele.files[i];
        let media = {
          url: URL.createObjectURL(file),
          size: file.size,
          ext: file.name.split(".")[1],
          name: file.name.split(".")[0],
          width: 0,
          height: 0,
          file: file,
          status: 1,
          message: "点击上传",
        };
        this.picSrc = media.url
        if (index === -1) {
          this.fileList.push(media);
          console.log(this.fileList);
        }
      }
      ele.value = "";
    },
    addMedia(media) {
      // console.log(media);
      if (media.status == 3) return media;
      media.status = 2;
      let file =
        media.file instanceof File
          ? media.file
          : new File([media.file], media.file.name, { type: media.file.type });

      return this.$api.media.upload
        .request(
          {
            type: "image",
            size: media.size,
            width: media.width,
            height: media.height,
          },
          file,
          (p) => {
            media.message = p + "%";
          }
        )
        .then((res) => {
          // 修改图标地址
          this.form.logo = res.url
          media.message = "已上传";
          media.status = 3;

          return {
            url: res.url,
            width: media.width,
            height: media.height,
            size: media.size,
          };
        })
        .catch((e) => {
          media.message = e.message || e;
          media.status = 4;
        });
    },
    successUpload(){
      
      this.fileList.map((data) => {
        return this.addMedia(data);
      });
    },
    cancelUpload(){
      this.hasPic = true
      this.picSrc = ""
    }
  },
};
</script>
<style lang="scss">
.searchBox {
  margin-bottom: 10px;
}
.searchName-val {
  margin-right: 15px;
}
.logo {
  width: 40px;
  height: 40px;
}
.img-item{
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #ccc;
}
.img-item2{
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.imgTu{
  width: 100px;
  height: 100px;
}
.btns{
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

</style>
