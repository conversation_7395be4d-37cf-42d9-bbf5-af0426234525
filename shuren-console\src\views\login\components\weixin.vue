<template>
  <div class="login-container is-weixin">
    <div class="login-header">
      <div class="login-type">
        <label class="login-label active">微信登录</label>
      </div>
      <div class="switch-type">
        <label class="switch-label">密码登录</label>
        <div class="bypwd" @click="$emit('change', 'password')"></div>
      </div>
    </div>
    <div class="login-body">
      <div>
        <img class="qrimg" :src="qrcode" alt="">
        <div class="scanmsg">{{ scanmsg }}</div>
      </div>
      <div class="refresh-qrcode" v-show="invalid">
        <div>
          <div>二维码已失效</div>
          <el-button type="primary" size="small" @click="generate">点击刷新</el-button>
        </div>
      </div>
    </div>
    <div class="login-footer text-center">请打开微信使用【扫一扫】扫描上面的二维码</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      qrcode: '',
      timer: 0,
      invalid: 0,
      number: 180,
      scanmsg: ''
    }
  },
  beforeDestroy() {
    this.clear();
  },
  mounted() {
    this.generate();
  },
  methods: {
    generate() {
      clearInterval(this.timer);

      this.invalid = 0;
      this.scanmsg = '正在生成二维码';
      this.$require('qrcode').then(() => {
        return QRCode.toDataURL(this.$env.APP_URL + '/login', {
          width: 250,
          height: 250,
          margin: 0
        });
      }).then(url => {
        this.qrcode = url;
        this.loopResult();
      }).catch(e => {
        this.invalid = 1;
        this.scanmsg = '生成二维码失败';
      });
    },
    loopResult() {
      this.number = 180;
      this.scanmsg = '二维码已生成';
      this.timer = setInterval(this.getResult, 1000);
    },
    clear() {
      clearInterval(this.timer);
      this.invalid = 1;
    },
    getResult() {
      let i = --this.number;
      this.scanmsg = '3分钟后过期，剩' + i + '秒';

      if (i < 1) {
        this.clear();
      } else if (i < 175 && i % 2 === 0) {
        console.log('轮询请求登录结果：' + this.number);
      }
    }
  }
}
</script>