<template>
  <div v-loading="!!status">
    <div class="edit-panel-title">清理缓存</div>

    <div class="edit-panel-body" style="background: #f0f0f0;padding: 20px;">
      <p style="font-weight: bold">【擦除所有数据】</p>
      <p>1.仅当一体机存储空间不足或发生无法解决故障时使用</p>
      <p>2.将清除掉所有缓存的数据和文件，不影响云服务器</p>
      <p>3.清理完成后系统会自动重启</p>
      <p>4.重启后需<span style="color:red;font-weight:bold">【重新登录】</span>并重新<span style="color:red;font-weight:bold">【更新数据】</span></p>
      <p style="font-weight: bold">【仅清理数据库】</p>
      <p>1.仅当APP升级无法兼容旧版本时使用</p>
      <p>2.系统将删除旧数据库，并自动重启</p>
      <p>3.重启后需重新<span style="color:red;font-weight:bold">【更新数据】</span></p>
    </div>

    <div class="edit-panel-action">
      <template v-if="status===0">
        <el-button size="small" @click="clean('database')">仅清理数据库</el-button>
        <el-button type="danger" size="small" @click="clean('all')">擦除所有数据</el-button>
      </template>
      <el-button v-if="status===1" type="primary" size="small">清理中</el-button>
      <el-button v-if="status===2" type="success" size="small">清理成功</el-button>
      <el-button v-if="status===3" type="success" size="small">清理失败</el-button>
      <el-button v-if="status===4" type="warning" size="small">已超时</el-button>
    </div>
  </div>
</template>

<script>
import BasePanel from './base-panel';

export default {
  extends: BasePanel,
  data() {
    return {
      timer: 0,
      status: 0
    }
  },
  mounted() {
    this.socket.on('app clean state', (res) => {
      this.status = 2;
      clearTimeout(this.timer);

      setTimeout(function () {
        this.status = res.state == 'success' ? 2 : 3;
      }, 5000)
    });
  },
  methods: {
    clean(type) {
      this.status = 1;
      this.timer = setTimeout(() => {
        this.status = 4;

        setTimeout(function () {
          this.status = 0;
        }, 5000);
      }, 180000);

      this.socket.emit('notify app clean', type);
    }
  }
}
</script>