<template>
  <div class="empty-box">
    <img :src="icon" class="empty-img">
    <div class="empty-label">{{ label }}</div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'ElEmpty',
  props: {
    label: {
      type: String,
      default: '暂无数据'
    },
    icon: {
      type: String,
      default(){
        return this.$env.CDN_URL + '/sys/empty.png'
      }
    }
  }
}
</script>