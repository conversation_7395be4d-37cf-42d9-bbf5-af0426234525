import request from './request';

const captcha = {
	sms: data => {
		return request.post('project.captcha', data);
	},
	country: '+86',
  countrys: [
    {name: '中国大陆', mobile_preg: /^(\+?0?86\-?)?1[3456789]\d{9}$/, mobile_prefix: '+86'},
    {name: '中国台湾', mobile_preg: /^(\+?886\-?|0)?9\d{8}$/, mobile_prefix: '+886'},
    {name: '中国香港', mobile_preg: /^(\+?852\-?)?[456789]\d{3}\-?\d{4}$/, mobile_prefix: '+852'},
    {name: '中国澳门', mobile_preg: /^(\+?853\-?)?1[3456789]\d{9}$/, mobile_prefix: '+853'},
	],
	splitMobile(mobile){
		if(!mobile){
			return false;
		}

    let list = this.countrys;

    for(let i=0; i<list.length; i++){
      if(mobile.startsWith(list[i].mobile_prefix) && list[i].mobile_preg.test(mobile)){
      	let data = list[i];
      	return {prefix: data.mobile_prefix, number: mobile.substr(data.mobile_prefix.length), country: data.name};
        // break;
      }
    }

    return false;
	},
  hidemobile(mobile){
		let res = this.splitMobile(mobile);
		if(res === false){
			return '';
		}

    let number = res.number;

		return number.substr(0, 3) + '****' + number.substr(number.length - 4);
  }
};

export default captcha;