<template>
  <el-cascader
      v-model="value"
      :options="list"
      :props="{ checkStrictly: true, expandTrigger: 'hover', label: 'name', value: 'id' }"
      clearable
      filterable
      popper-class="dept-cascader"
      @change="onChange"></el-cascader>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      list: []
    }
  },
  created() {
    this.$api.department.list(this.$store.state.admin.company_id).then(list => {
      this.list = list;
    });
  },
  methods: {
    onChange(v) {
      this.$emit('input', v[v.length - 1] || 0);
    }
  }
}
</script>