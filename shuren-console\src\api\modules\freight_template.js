import request from './request';

export default {
  query(query){
    return request.get('freight_template.query', query).then(rows => {
      rows.forEach(item => {

        switch (item.unit) {
          case 0:
            item._flag = '件数';
            break;
          case 1:
            item._flag = '重量';
            break;
          case 2:
            item._flag = '体积';
            break;
        }

      });
      
      return rows;
    });
  }
}