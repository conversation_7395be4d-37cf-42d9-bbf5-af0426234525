import request from './request';
import utils from '@/utils';

function  format(item){
  item._modified = utils.Date(item.modified).toString("YYYY-MM-DD HH:mm");
  return item;
}

export default {
  query(query) {
    return request.get('media.navigation.query', query).then(res => {
      res.rows.forEach(format);
      return res;
    });
  },
  get(id) {
    return request.get('media.navigation.get?id=' + id);
  },
  add(data) {
    return request.post('media.navigation.add', data);
  },
  save(data) {
    return request.post('media.navigation.update', data);
  },
  delete(id) {
    return request.post('media.navigation.delete', {id: id});
  }
};