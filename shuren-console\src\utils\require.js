import Env from "./env";

let required = {},
  link = document.createElement('link'),
  relList = link.relList,
  isPreload, isPrefetch,
  npm = 'https://cdn.nextv.show/npm/',
  file = Env.CDN_URL,
  CNF = {
    clipboard: npm + 'clipboard@2.0.1/dist/clipboard.min.js',
    lrz: npm + 'lrz@4.9.40/dist/lrz.all.bundle.min.js',
    signature_pad: npm + 'signature_pad@2.3.2/dist/signature_pad.min.js',
    moment: npm + 'moment@2.22.2/moment.min.js',
    jsbarcode: npm + 'jsbarcode@3.11.0/dist/barcodes/JsBarcode.code128.min.js',
    qrcode: npm + 'qrcode@1.3.2/build/qrcode.min.js',
    amap: 'https://webapi.amap.com/maps?v=1.4.11&key=fedfdee92b1acc0ff122ed96ea83efcb',
    html2canvas: npm + 'html2canvas@1.0.0-alpha.12/dist/html2canvas.min.js',
    hls: npm + 'hls.js@latest',
    flvjs: 'flv.js@latest',
    sortable: npm + 'sortablejs@1.12.0/dist/sortable.umd.min.js',
    tinymce: npm + 'tinymce@5.1.1/tinymce.min.js',
    socket: 'https://cdn.nextv.show/npm/socket.io-client@3.1.0/dist/socket.io.min.js',
    player: [
      npm + 'hls.js@latest', npm + 'flv.js@latest', npm + 'three@0.114.0/build/three.min.js',
      file + '/player/webvr.js?m=1',
      file + '/player/main.js?m=8',
      file + '/player/style.css?m=7',
      file + '/player/iconfont.js'
    ],
    krpano: [file + '/pano/iconfont.js', file + '/pano/main.js'],
    qiniu: 'https://cdn.nextv.show/npm/qiniu-js@2.5.5/dist/qiniu.min.js'
  };

if (relList && relList.supports) {
  isPreload = relList.supports('preload');
  isPrefetch = relList.supports('prefetch');
}

// 遍历URL，加载关联
function eachUrl(list) {
  if (typeof list == 'string') list = [list];

  let array = [], key, i = 0;
  for (; i < list.length; i++) {
    key = list[i];
    if (key instanceof Array) {
      array = array.concat(eachUrl(key));
    } else if (CNF[key]) {
      if (CNF[key] instanceof Array) {
        array = array.concat(eachUrl(CNF[key]));
      } else {
        array.push(CNF[key]);
      }
    } else {
      array.push(key);
    }
  }

  return array;
}

// 解析URL类型
function parseUrl(url) {
  if (required[url]) return required[url];

  let match = url.match(/[0-9a-zA-Z_\.]+\.(\w+)(\?.*|#.*)?$/i)
    , tagName = 'script'
    , as = 'script'
    , type = 'text/javascript'
    , anonymous = true;

  if (match) {
    switch (match[1]) {
      case 'js':
        break;
      case 'css':
        tagName = 'link';
        as = 'style';
        type = 'text/css';
        break;
    }
  } else {
    anonymous = false;
  }

  return required[url] = {tagName: tagName, as: as, type: type, anonymous: anonymous};
}

function preload(url, retry) {
  let uri = parseUrl(url);
  if (uri.loaded) return;

  let link = document.createElement('link');
  if (uri.anonymous) link.crossOrigin = 'anonymous';
  link.rel = isPreload ? 'preload' : 'prefetch';
  link.as = uri.as;
  link.type = uri.type;
  link.referrerpolicy = 'no-referrer';

  link.onload = function () {
    this.remove();
  };

  link.onerror = function () {
    this.remove();

    // 重试一次
    if (retry !== false) {
      setTimeout(function () {
        preload(url, false);
      }, 1200);
    }
  };

  link.href = url;
  document.body.appendChild(link);
}

function preimage(url) {
  return new Promise(function (resolve, reject) {
    let uri = parseUrl(url);
    if (uri.loaded) return resolve();

    let img = new Image();
    img.crossOrigin = 'anonymous';
    img.referrerpolicy = 'no-referrer';

    function loaded() {
      resolve();
      img = null;
    }

    img.onload = loaded;
    img.onerror = loaded;
  });
}

// 加载解析
function execload(url, callback, retry) {
  let uri = parseUrl(url);
  if (uri.loaded) return callback();

  let el = document.createElement(uri.tagName);
  if (uri.anonymous) el.crossOrigin = 'anonymous';
  el.referrerpolicy = 'no-referrer';

  el.onload = function () {
    el.tagName == 'SCRIPT' && el.remove();
    uri.loaded = true;
    callback();
  };

  el.onerror = function () {
    el.remove();

    // 重试一次
    if (retry !== false) {
      setTimeout(function () {
        execload(url, callback, false);
      }, 1000);
    } else {
      callback(false);
    }
  };

  switch (uri.tagName) {
    case 'script':
      el.type = uri.type;
      el.src = url;
      break;
    case 'link':
      el.rel = 'stylesheet';
      el.type = uri.type;
      el.href = url;
      break;
  }

  document.head.appendChild(el);
}

// 预加载
export function preloadjs() {
  let fn = (isPreload || isPrefetch) ? preload : preimage;
  eachUrl(arguments).forEach(function (url) {
    fn(url);
  });
}

// 异步加载文件
function requirejs() {
  let list = arguments;
  return new Promise(function (resolve, reject) {
    list = eachUrl(list);

    function _execload(i) {
      execload(list[i], function (err) {
        if (err) return reject(new Error('加载插件失败'));
        if (++i == list.length) return resolve();
        _execload(i);
      });
    }

    _execload(0);
  });
}

export default requirejs;