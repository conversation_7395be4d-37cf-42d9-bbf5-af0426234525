<template>
  <div :id="mapid" class="addr-map-box">
    <input v-if="!input" :id="search" class="map-input" autocomplete="off" placeholder="请输入关键字"/>
  </div>
</template>

<style lang="scss">
.address-map-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.addr-map-box {
  width: 100%;
  height: 100%;
  min-height: 320px;

  .map-input {
    z-index: 999;
    position: absolute;
    top: 15px;
    right: 15px;
  }
}

.amap-sug-result {
  z-index: 9999;
}
</style>

<script>
function random() {
  return Math.random().toString().substr(2);
}

export default {
  props: {
    lng: {
      type: Number,
      default: 0
    },
    lat: {
      type: Number,
      default: 0
    },
    province_code: {
      type: Number,
      default: 0
    },
    city_code: {
      type: Number,
      default: 0
    },
    district_code: {
      type: Number,
      default: 0
    },
    province_name: {
      type: String,
      default: ''
    },
    city_name: {
      type: String,
      default: ''
    },
    district_name: {
      type: String,
      default: ''
    },
    address: {
      type: String,
      default: ''
    },
    input: {
      type: String
    }
  },
  data() {
    return {
      mapid: 'mapid' + random(),
      search: 'search' + random()
    }
  },
  computed: {
    inputEl() {
      return document.getElementById(this.input || this.search);
    }
  },
  watch: {
    district_code(v){
      if (!this.autocomplete) return;
      this.autocomplete.setCity(v);
      this.placeSearch.setCity(v);
    }
  },
  mounted() {
    this.$require('amap').then(() => this.init());
  },
  methods: {
    showError(msg) {
      this.$notify.warning({title: '警告', message: msg || '解析位置失败'});
    },
    init() {
      let {lng, lat} = this;

      // 初始化地图
      let map = new AMap.Map(this.mapid, {
        resizeEnable: true,
        center: lng ? [lng, lat] : null,
        zoom: 18
      });

      // 左侧工具栏
      map.plugin("AMap.ToolBar", function () {
        let toolBar = new AMap.ToolBar({visible: true});
        map.addControl(toolBar);
      });

      this.map = map;

      this.resetCenter().then(() => {
        AMap.plugin(['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Geocoder'], this.initSearch);
      });
    },
    resetCenter() {
      let {lng, lat, map} = this;

      return new Promise((resolve) => {
        if (lng && lat) {
          map.setCenter([lng, lat]);
          return resolve();
        }

        let city = this.district_code || this.city_code || this.province_code || this.city_name || this.province_name;
        city ? map.setCity(city, resolve) : resolve();
      });
    },
    initSearch() {
      let scope = this, {map} = this;

      let city = this.district_code || this.city_code || this.province_code || this.city_name || this.province_name || '全国';
      let marker = new AMap.Marker({icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png"});
      let autocomplete = new AMap.Autocomplete({input: this.inputEl, city: city});
      let placeSearch = new AMap.PlaceSearch({map: map});
      let geocoder = new AMap.Geocoder({timeout: 30000});
      let parseAddress = function (location) {// 设置搜索结果
        marker.setPosition(location);
        map.add(marker);
        map.setCenter(location);
        map.getCity(function (res) {
          placeSearch.setCity(res.city);
        });

        let isArr = location instanceof Array;
        let lng = isArr ? location[0] : location.lng;
        let lat = isArr ? location[1] : location.lat;

        geocoder.getAddress(location, function (status, result) {
          if (status == 'error') return scope.showError();
          if (status != 'complete') return;
          if (result.info != 'OK') return scope.showError();

          let res = result.regeocode.addressComponent;
          let detail = scope.lng == lng && scope.lat == lat
              ? scope.address
              : result.regeocode.formattedAddress.substr(res.province.length + res.city.length + res.district.length);

          let model = {
            lng: lng,
            lat: lat,
            province_code: parseInt(res.adcode.substr(0, 3) + '000'),
            city_code: parseInt(res.adcode.substr(0, 4) + '00'),
            district_code: parseInt(res.adcode),
            province_name: res.province,
            city_name: res.city,
            district_name: res.district,
            address: detail
          };

          for (let field in model) {
            scope[field] = model[field];
            scope.$emit('update:' + field, model[field]);
          }
        });
      };

      this.autocomplete = autocomplete;
      this.placeSearch = placeSearch;

      // 解析当前中心点
      parseAddress(map.getCenter());

      // 根据输入提示搜索
      AMap.event.addListener(autocomplete, 'select', function (e) {
        let location = e.poi.location;
        location ? parseAddress(location) : scope.showError('请选择一个有效的地址');
      });

      // 点击地图添加标注点
      map.on('click', (e) => parseAddress(e.lnglat));

      // 按回车搜索
      this.inputEl.addEventListener('keypress', function (e) {
        if (e.key != 'Enter') return;

        let name = e.target.value.replace(/\s/g);
        if (name.length < 2) {
          scope.$notify.warning({title: '警告', message: '关键字过短'});
        }

        placeSearch.search(name, (status, result) => {
          if (status != 'complete') return;

          if (result.poiList.count == 0) {
            return scope.showError('未匹配到相关地址');
          }

          parseAddress(result.poiList.pois[0].location);
        })
      });
    }
  }
}
</script>