<template>
  <div class="layout-tabs" @mousewheel.prevent.stop="mousewheel">
    <div class="tabs-left" @mousedown.stop.prevent="mouseDown(0)">
      <i class="el-icon-d-arrow-left"></i>
    </div>
    <div class="tabs-list" ref="list">
      <div
        class="tabs-item"
        v-for="item in list"
        :class="{ active: item.path == active.path }"
        @click.stop.prevent="click(item)"
      >
        <div class="tabs-name">{{ item.title }}</div>
        <div class="tabs-full-name">
          <div class="scroll-name">{{ item.title }}</div>
          <div class="scroll-name">{{ item.title }}</div>
        </div>
        <i class="el-icon-circle-close" @click.stop.prevent="close(item)"></i>
      </div>
    </div>
    <div class="tabs-right" @mousedown.stop.prevent="mouseDown(1)">
      <i class="el-icon-d-arrow-right"></i>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    list() {
      return this.$store.state.nav.routes;
    },
    active() {
      return this.$store.state.nav.route;
    },
  },
  watch: {
    active(v) {
      this.$nextTick(() => {
        this.moveTo(v.path);
      });
    },
  },
  methods: {
    mousewheel(e) {
      this.$refs.list.scrollLeft += e.wheelDelta > 0 ? -100 : 100;
    },
    mouseDown(v) {
      let el = v ? this.moveRight() : this.moveLeft(),
        timer;

      window.addEventListener(
        "mouseup",
        function () {
          cancelAnimationFrame(timer);
        },
        { once: true, capture: true, passive: true }
      );

      timer = requestAnimationFrame(function move() {
        el.scrollLeft += (v ? 1 : -1) * 6;

        timer = requestAnimationFrame(move);
      });
    },
    moveLeft() {
      let el = this.$refs.list,
        val = 0,
        max = 120,
        step = 10;

      requestAnimationFrame(function scroll() {
        val += step;
        if (val <= max) {
          el.scrollLeft -= step;
          requestAnimationFrame(scroll);
        }
      });

      return el;
    },
    moveRight() {
      let el = this.$refs.list,
        val = 0,
        max = 120,
        step = 10;

      requestAnimationFrame(function scroll() {
        val += step;
        if (val <= max) {
          el.scrollLeft += step;
          requestAnimationFrame(scroll);
        }
      });

      return el;
    },
    moveTo(path) {
      let { list } = this,
        len = list.length,
        i = 0,
        el = this.$refs.list;

      for (; i < len; i++) {
        if (list[i].path == path) {
          break;
        }
      }

      let active = el.children[i],
        offset = active.offsetLeft,
        width = active.clientWidth,
        scroll = el.scrollLeft,
        client = el.clientWidth,
        marginLeft = offset - scroll;

      if (marginLeft < 0) {
        el.scrollLeft = offset;
      } else if (marginLeft + width > client) {
        el.scrollLeft = offset + width - client;
      }
    },
    close(route) {
      this.$store.dispatch("nav/close", route).then((_) => {
        let { history } = this.$store.state.nav;
        this.$router.push(history[history.length - 1]);
      });
    },
    click(route) {
      this.$router.push(route.path);
    },
  },
};
</script>